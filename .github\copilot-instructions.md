# Copilot Instructions for eCommerce Website

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Context
This is a modern eCommerce website built with Next.js 14 using the App Router, TypeScript, and TailwindCSS. The project includes:

- **Frontend**: React with Next.js 14 (App Router)
- **Authentication**: Firebase Auth with role-based access (admin/customer)
- **Database**: Firebase Firestore for products, users, and orders
- **UI Components**: shadcn/ui for consistent design system
- **Styling**: TailwindCSS for responsive design
- **Animations**: Framer Motion for smooth transitions
- **Hosting**: Firebase Hosting

## Key Features
- Product listing with filters and search
- Shopping cart functionality
- User authentication with role-based access
- Admin panel for product management
- Order management system
- Responsive design for mobile and desktop

## Code Guidelines
- Use TypeScript for all components and utilities
- Follow Next.js 14 App Router patterns
- Implement proper error handling and loading states
- Use Framer Motion for animations
- Follow shadcn/ui component patterns
- Ensure responsive design with TailwindCSS
- Implement proper Firebase security rules
- Use server actions for Firebase operations where appropriate

## File Structure
- `/src/app` - Next.js App Router pages
- `/src/components` - Reusable React components
- `/src/lib` - Utility functions and configurations
- `/src/types` - TypeScript type definitions
- `/src/firebase` - Firebase configuration and utilities
