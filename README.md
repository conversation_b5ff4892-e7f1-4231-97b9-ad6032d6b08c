# E-Commerce Platform

A modern, full-featured e-commerce platform built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Features

### Admin Dashboard (`/admin`)
- **Product Management**: Full CRUD operations for products with Cloudinary image upload
- **Order Management**: Track and manage customer orders with status updates
- **Customer Communication**: Handle customer inquiries and support messages
- **Analytics Dashboard**: View sales statistics and business metrics
- **User Role Management**: Automatic admin role assignment for specified email

### Customer Dashboard (`/dashboard`)
- **Personal Dashboard**: View order history, spending statistics, and account overview
- **Order Tracking**: Monitor order status and delivery updates
- **Profile Management**: Update personal information and preferences
- **Favorites & Wishlist**: Save and manage favorite products
- **Shopping Cart**: Add items, apply coupons, and checkout
- **Reward Points**: Earn and track loyalty points

### Public Features
- **Product Browsing**: Search, filter, and browse products by category
- **Shopping Cart**: Add products and manage quantities
- **User Authentication**: Sign up, login, and secure authentication
- **Contact System**: Customer support form with categorized inquiries
- **Responsive Design**: Mobile-first design with smooth animations

## 🛠️ Technology Stack

- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **Authentication**: Firebase Authentication
- **Database**: Firebase Firestore (ready for integration)
- **Image Upload**: Cloudinary integration
- **Animations**: Framer Motion
- **Icons**: Lucide React

## 🔧 Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```

4. Configure your Firebase and Cloudinary credentials in `.env.local`

5. Run the development server:
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
src/
├── app/
│   ├── admin/                 # Admin dashboard
│   ├── dashboard/             # Customer dashboard
│   ├── products/              # Product catalog
│   ├── cart/                  # Shopping cart
│   ├── contact/               # Contact form
│   ├── login/                 # Authentication
│   ├── signup/                # Registration
│   └── api/                   # API routes
├── components/
│   ├── ui/                    # Reusable UI components
│   ├── ProductManagement.tsx  # Admin product management
│   ├── OrderManagement.tsx    # Admin order management
│   ├── CustomerCommunication.tsx # Admin customer support
│   ├── CartComponent.tsx      # Shopping cart component
│   ├── ContactForm.tsx        # Contact form component
│   ├── Header.tsx             # Navigation header
│   └── Footer.tsx             # Site footer
├── contexts/
│   └── AuthContext.tsx        # Authentication context
├── lib/
│   ├── actions/               # Server actions
│   ├── cloudinary.ts          # Cloudinary configuration
│   └── utils.ts               # Utility functions
├── firebase/
│   ├── config.ts              # Firebase configuration
│   └── admin.ts               # Firebase admin SDK
└── types/
    └── index.ts               # TypeScript type definitions
```

## 🔐 User Roles

### Admin Role
- **Email**: `<EMAIL>` (automatically assigned admin role)
- **Access**: Full admin dashboard with all management features
- **Permissions**: Product CRUD, order management, customer communication

### Customer Role
- **Access**: Customer dashboard with personal features
- **Permissions**: Order tracking, profile management, shopping cart

## 📊 Dashboard Features

### Admin Dashboard
1. **Analytics Overview**: Sales statistics, order counts, revenue tracking
2. **Product Management**: Add, edit, delete products with image upload
3. **Order Processing**: View and update order status, track shipments
4. **Customer Support**: Read and reply to customer messages
5. **User Management**: View registered users (coming soon)

### Customer Dashboard
1. **Account Overview**: Order history, spending totals, reward points
2. **Order Tracking**: Real-time order status updates
3. **Profile Settings**: Personal information management
4. **Favorites**: Wishlist and saved products
5. **Address Book**: Shipping and billing addresses
6. **Payment Methods**: Saved payment options

## 🎨 UI Components

Built with **shadcn/ui** components:
- Cards, Buttons, Forms, Modals
- Dropdowns, Badges, Loading states
- Responsive navigation and layouts
- Smooth animations with Framer Motion

## 🚀 Getting Started

1. **First Time Setup**:
   - Sign up with any email for customer access
   - Sign up with `<EMAIL>` for admin access

2. **Admin Features**:
   - Access `/admin` for full dashboard
   - Manage products, orders, and customers
   - View analytics and business metrics

3. **Customer Features**:
   - Access `/dashboard` for personal dashboard
   - Browse products at `/products`
   - Add items to cart and checkout
   - Contact support via `/contact`

## 🔄 API Routes

- `GET /api/products` - Fetch all products
- `POST /api/products` - Create new product (Admin only)
- `GET /api/orders` - Fetch orders
- `POST /api/orders` - Create new order
- `GET /api/messages` - Fetch customer messages
- `POST /api/messages` - Send customer message
- `GET /api/users` - Fetch users (Admin only)

## 🎯 Future Enhancements

- Real-time notifications
- Advanced analytics dashboard
- Inventory management
- Multi-language support
- Payment gateway integration
- Mobile app version

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License.

---

Built with ❤️ using Next.js and modern web technologies.
