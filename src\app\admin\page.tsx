"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import ProductManagement from '@/components/ProductManagement';
import OrderManagement from '@/components/OrderManagement';
import CustomerCommunication from '@/components/CustomerCommunication';
import RoleGuard from '@/components/RoleGuard';
import { 
  Package, 
  Users, 
  ShoppingCart, 
  DollarSign, 
  BarChart3,
  Settings,
  Bell,
  Calendar,
  Eye,
  MessageSquare,
  TrendingUp,
  Activity
} from 'lucide-react';
import { DashboardStats } from '@/types';

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.3 }
};

const stagger = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function AdminPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch dashboard stats
  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      // Mock dashboard stats - in real app, this would be API calls
      const mockStats: DashboardStats = {
        totalProducts: 156,
        totalUsers: 1247,
        totalOrders: 89,
        totalRevenue: 45670,
        pendingOrders: 23,
        unreadMessages: 12,
        lowStockProducts: 8,
        topProducts: [],
        recentOrders: [],
        monthlyRevenue: [3200, 4100, 3800, 4500, 5200, 4800, 5500, 6200, 5800, 6500, 7200, 8100],
        monthlyOrders: [45, 52, 48, 61, 68, 59, 72, 81, 73, 84, 92, 105]
      };
      setStats(mockStats);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'products', label: 'Products', icon: Package },
    { id: 'orders', label: 'Orders', icon: ShoppingCart },
    { id: 'messages', label: 'Messages', icon: MessageSquare },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const StatCard = ({ title, value, icon: Icon, color, change }: {
    title: string;
    value: string | number;
    icon: any;
    color: string;
    change?: string;
  }) => (
    <motion.div variants={fadeInUp}>
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
              {change && (
                <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                  <TrendingUp className="h-3 w-3" />
                  {change}
                </p>
              )}
            </div>
            <div className={`p-3 rounded-full ${color}`}>
              <Icon className="h-6 w-6 text-white" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <RoleGuard requiredRole="admin">
      <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
      {/* Navigation */}
      <div className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold">Admin Dashboard</h1>
              <Badge variant="outline" className="bg-blue-50 text-blue-600">
                {user?.name}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Navigation</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {tabItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-100 transition-colors ${
                        activeTab === item.id ? 'bg-primary/10 text-primary border-r-2 border-primary' : ''
                      }`}
                    >
                      <item.icon className="h-4 w-4" />
                      {item.label}
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeTab === 'dashboard' && (
              <div className="space-y-8">
                {/* Stats Cards */}
                {loading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {Array(4).fill(0).map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-6">
                          <div className="h-16 bg-gray-200 rounded"></div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <motion.div
                    initial="initial"
                    animate="animate"
                    variants={stagger}
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
                  >
                    <StatCard
                      title="Total Products"
                      value={stats?.totalProducts || 0}
                      icon={Package}
                      color="bg-blue-500"
                      change="+12% from last month"
                    />
                    <StatCard
                      title="Total Orders"
                      value={stats?.totalOrders || 0}
                      icon={ShoppingCart}
                      color="bg-green-500"
                      change="+8% from last month"
                    />
                    <StatCard
                      title="Total Revenue"
                      value={`$${stats?.totalRevenue.toLocaleString() || 0}`}
                      icon={DollarSign}
                      color="bg-purple-500"
                      change="+15% from last month"
                    />
                    <StatCard
                      title="Total Users"
                      value={stats?.totalUsers || 0}
                      icon={Users}
                      color="bg-orange-500"
                      change="+5% from last month"
                    />
                  </motion.div>
                )}

                {/* Quick Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Button 
                        onClick={() => setActiveTab('orders')}
                        className="justify-start h-auto p-4"
                        variant="outline"
                      >
                        <div className="text-left">
                          <p className="font-medium">Pending Orders</p>
                          <p className="text-sm text-gray-600">{stats?.pendingOrders || 0} orders need attention</p>
                        </div>
                      </Button>
                      <Button 
                        onClick={() => setActiveTab('messages')}
                        className="justify-start h-auto p-4"
                        variant="outline"
                      >
                        <div className="text-left">
                          <p className="font-medium">Unread Messages</p>
                          <p className="text-sm text-gray-600">{stats?.unreadMessages || 0} messages to review</p>
                        </div>
                      </Button>
                      <Button 
                        onClick={() => setActiveTab('products')}
                        className="justify-start h-auto p-4"
                        variant="outline"
                      >
                        <div className="text-left">
                          <p className="font-medium">Low Stock</p>
                          <p className="text-sm text-gray-600">{stats?.lowStockProducts || 0} products need restocking</p>
                        </div>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'products' && <ProductManagement />}
            {activeTab === 'orders' && <OrderManagement />}
            {activeTab === 'messages' && <CustomerCommunication />}
            
            {activeTab === 'users' && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">User Management</h3>
                <p className="text-gray-600">User management features coming soon...</p>
              </div>
            )}
            
            {activeTab === 'settings' && (
              <div className="text-center py-12">
                <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Settings</h3>
                <p className="text-gray-600">Settings panel coming soon...</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    </RoleGuard>
  );
}

