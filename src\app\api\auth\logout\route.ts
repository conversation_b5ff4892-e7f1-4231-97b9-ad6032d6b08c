import { logout } from '@/lib/actions/auth.action';

export async function POST() {
    try {
        const result = await logout();
        
        if (result.success) {
            return Response.json({
                success: true,
                message: 'Logged out successfully'
            }, { status: 200 });
        } else {
            return Response.json({
                success: false,
                error: 'Logout failed'
            }, { status: 500 });
        }
    } catch (error) {
        console.error('Logout error:', error);
        return Response.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    }
}
