import { db } from '@/firebase/admin';

// Define the admin email
const ADMIN_EMAIL = '<EMAIL>';

export async function POST(request: Request) {
    try {
        const { name, email, uid } = await request.json();

        if (!name || !email || !uid) {
            return Response.json({
                success: false,
                error: 'Name, email, and uid are required'
            }, { status: 400 });
        }

        // Determine user role - admin if email matches, otherwise customer
        const role = email === ADMIN_EMAIL ? 'admin' : 'customer';

        // Create user profile in Firestore
        await db.collection("users").doc(uid).set({
            id: uid,
            name,
            email,
            role, // Set role based on email
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        });

        return Response.json({
            success: true,
            message: 'User profile created successfully',
            user: {
                id: uid,
                name,
                email,
                role
            }
        }, { status: 200 });

    } catch (error) {
        console.error('Error in signup API:', error);
        return Response.json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
    }
}
