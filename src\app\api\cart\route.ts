import { NextRequest, NextResponse } from 'next/server';
import { Cart, CartItem } from '@/types';
import { db } from '@/firebase/admin';

// GET - Fetch user's cart
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Get user's cart
    const cartQuery = await db.collection('carts')
      .where('userId', '==', userId)
      .limit(1)
      .get();
    
    if (cartQuery.empty) {
      // Create empty cart if none exists
      const newCart: Omit<Cart, 'id'> = {
        userId,
        items: [],
        total: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const docRef = await db.collection('carts').add(newCart);
      
      const cart: Cart = {
        id: docRef.id,
        ...newCart
      };
      
      return NextResponse.json(cart);
    }
    
    const cartDoc = cartQuery.docs[0];
    const cart: Cart = {
      id: cartDoc.id,
      ...cartDoc.data()
    } as Cart;
    
    return NextResponse.json(cart);
  } catch (error) {
    console.error('Error fetching cart:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cart' },
      { status: 500 }
    );
  }
}

// POST - Add item to cart
export async function POST(request: NextRequest) {
  try {
    const { userId, productId, quantity = 1 } = await request.json();
    
    if (!userId || !productId) {
      return NextResponse.json(
        { error: 'User ID and Product ID are required' },
        { status: 400 }
      );
    }
    
    // Get product details
    const productDoc = await db.collection('products').doc(productId).get();
    
    if (!productDoc.exists) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }
    
    const product = { id: productDoc.id, ...productDoc.data() };
    
    // Check if product is active
    if (!product.isActive) {
      return NextResponse.json(
        { error: 'Product is no longer available' },
        { status: 400 }
      );
    }

    // Check stock availability
    if (product.stock < quantity) {
      return NextResponse.json(
        { error: `Only ${product.stock} units available in stock` },
        { status: 400 }
      );
    }

    if (product.stock === 0) {
      return NextResponse.json(
        { error: 'Product is out of stock' },
        { status: 400 }
      );
    }
    
    // Get or create user's cart
    const cartQuery = await db.collection('carts')
      .where('userId', '==', userId)
      .limit(1)
      .get();
    
    let cartRef;
    let cartData: Cart;
    
    if (cartQuery.empty) {
      // Create new cart
      const newCart: Omit<Cart, 'id'> = {
        userId,
        items: [],
        total: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      cartRef = await db.collection('carts').add(newCart);
      cartData = { id: cartRef.id, ...newCart };
    } else {
      const cartDoc = cartQuery.docs[0];
      cartRef = cartDoc.ref;
      cartData = { id: cartDoc.id, ...cartDoc.data() } as Cart;
    }
    
    // Check if item already exists in cart
    const existingItemIndex = cartData.items.findIndex(item => item.productId === productId);
    
    if (existingItemIndex >= 0) {
      // Update existing item quantity
      cartData.items[existingItemIndex].quantity += quantity;
      
      // Check if new quantity exceeds stock
      if (cartData.items[existingItemIndex].quantity > product.stock) {
        return NextResponse.json(
          { error: 'Quantity exceeds available stock' },
          { status: 400 }
        );
      }
    } else {
      // Add new item to cart
      const newCartItem: CartItem = {
        id: `item_${Date.now()}_${productId}`,
        productId,
        product,
        quantity,
        price: product.price
      };
      
      cartData.items.push(newCartItem);
    }
    
    // Recalculate total
    cartData.total = cartData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cartData.updatedAt = new Date().toISOString();
    
    // Update cart in database
    await cartRef.update({
      items: cartData.items,
      total: cartData.total,
      updatedAt: cartData.updatedAt
    });
    
    return NextResponse.json(cartData);
  } catch (error) {
    console.error('Error adding item to cart:', error);
    return NextResponse.json(
      { error: 'Failed to add item to cart' },
      { status: 500 }
    );
  }
}

// PUT - Update cart item quantity
export async function PUT(request: NextRequest) {
  try {
    const { userId, productId, quantity } = await request.json();
    
    if (!userId || !productId || quantity < 0) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }
    
    // Get user's cart
    const cartQuery = await db.collection('carts')
      .where('userId', '==', userId)
      .limit(1)
      .get();
    
    if (cartQuery.empty) {
      return NextResponse.json(
        { error: 'Cart not found' },
        { status: 404 }
      );
    }
    
    const cartDoc = cartQuery.docs[0];
    const cartData = cartDoc.data() as Cart;
    
    // Find the item in cart
    const itemIndex = cartData.items.findIndex(item => item.productId === productId);
    
    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'Item not found in cart' },
        { status: 404 }
      );
    }
    
    if (quantity === 0) {
      // Remove item from cart
      cartData.items.splice(itemIndex, 1);
    } else {
      // Check stock availability
      const productDoc = await db.collection('products').doc(productId).get();
      const product = productDoc.data();
      
      if (product && quantity > product.stock) {
        return NextResponse.json(
          { error: 'Quantity exceeds available stock' },
          { status: 400 }
        );
      }
      
      // Update quantity
      cartData.items[itemIndex].quantity = quantity;
    }
    
    // Recalculate total
    cartData.total = cartData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cartData.updatedAt = new Date().toISOString();
    
    // Update cart in database
    await cartDoc.ref.update({
      items: cartData.items,
      total: cartData.total,
      updatedAt: cartData.updatedAt
    });
    
    const updatedCart: Cart = {
      id: cartDoc.id,
      ...cartData
    };
    
    return NextResponse.json(updatedCart);
  } catch (error) {
    console.error('Error updating cart:', error);
    return NextResponse.json(
      { error: 'Failed to update cart' },
      { status: 500 }
    );
  }
}

// DELETE - Clear cart or remove specific item
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const productId = searchParams.get('productId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Get user's cart
    const cartQuery = await db.collection('carts')
      .where('userId', '==', userId)
      .limit(1)
      .get();
    
    if (cartQuery.empty) {
      return NextResponse.json(
        { error: 'Cart not found' },
        { status: 404 }
      );
    }
    
    const cartDoc = cartQuery.docs[0];
    const cartData = cartDoc.data() as Cart;
    
    if (productId) {
      // Remove specific item
      cartData.items = cartData.items.filter(item => item.productId !== productId);
    } else {
      // Clear entire cart
      cartData.items = [];
    }
    
    // Recalculate total
    cartData.total = cartData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cartData.updatedAt = new Date().toISOString();
    
    // Update cart in database
    await cartDoc.ref.update({
      items: cartData.items,
      total: cartData.total,
      updatedAt: cartData.updatedAt
    });
    
    const updatedCart: Cart = {
      id: cartDoc.id,
      ...cartData
    };
    
    return NextResponse.json(updatedCart);
  } catch (error) {
    console.error('Error deleting from cart:', error);
    return NextResponse.json(
      { error: 'Failed to delete from cart' },
      { status: 500 }
    );
  }
}
