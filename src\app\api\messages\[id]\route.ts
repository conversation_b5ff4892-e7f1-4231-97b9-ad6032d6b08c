import { NextRequest, NextResponse } from 'next/server';
import { ContactMessage, MessageReply } from '@/types';

// Mock data (in a real app, this would be from a database)
let mockMessages: ContactMessage[] = [];
let mockReplies: MessageReply[] = [];

// GET /api/messages/[id] - Get a specific message with replies
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const messageId = params.id;
    
    // Find the message
    const message = mockMessages.find(m => m.id === messageId);
    
    if (!message) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }
    
    // Get replies for this message
    const replies = mockReplies.filter(r => r.messageId === messageId);
    
    return NextResponse.json({
      message,
      replies: replies.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
    });
  } catch (error) {
    console.error('Error fetching message:', error);
    return NextResponse.json(
      { error: 'Failed to fetch message' },
      { status: 500 }
    );
  }
}

// PUT /api/messages/[id] - Update message status
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const messageId = params.id;
    const body = await request.json();
    const { status, priority } = body;
    
    // Find the message
    const messageIndex = mockMessages.findIndex(m => m.id === messageId);
    
    if (messageIndex === -1) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }
    
    // Update the message
    const updatedMessage = {
      ...mockMessages[messageIndex],
      ...(status && { status }),
      ...(priority && { priority }),
      updatedAt: new Date().toISOString()
    };
    
    mockMessages[messageIndex] = updatedMessage;
    
    return NextResponse.json(updatedMessage);
  } catch (error) {
    console.error('Error updating message:', error);
    return NextResponse.json(
      { error: 'Failed to update message' },
      { status: 500 }
    );
  }
}

// POST /api/messages/[id]/reply - Reply to a message
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const messageId = params.id;
    const body = await request.json();
    const { content, adminId } = body;
    
    // Find the message
    const messageIndex = mockMessages.findIndex(m => m.id === messageId);
    
    if (messageIndex === -1) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }
    
    // Create reply
    const newReply: MessageReply = {
      id: `reply_${Date.now()}`,
      messageId,
      adminId,
      content,
      createdAt: new Date().toISOString()
    };
    
    mockReplies.push(newReply);
    
    // Update message status to replied
    mockMessages[messageIndex] = {
      ...mockMessages[messageIndex],
      status: 'replied',
      updatedAt: new Date().toISOString()
    };
    
    return NextResponse.json(newReply, { status: 201 });
  } catch (error) {
    console.error('Error creating reply:', error);
    return NextResponse.json(
      { error: 'Failed to create reply' },
      { status: 500 }
    );
  }
}
