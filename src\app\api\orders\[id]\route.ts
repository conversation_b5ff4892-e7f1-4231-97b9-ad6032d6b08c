import { NextRequest, NextResponse } from 'next/server';
import { Order } from '@/types';

// Import mock orders (in a real app, this would be from a database)
let mockOrders: Order[] = []; // This would be imported from the main orders route

// GET /api/orders/[id] - Get a specific order
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const orderId = params.id;
    
    // Find the order
    const order = mockOrders.find(o => o.id === orderId);
    
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(order);
  } catch (error) {
    console.error('Error fetching order:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order' },
      { status: 500 }
    );
  }
}

// PUT /api/orders/[id] - Update an order
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const orderId = params.id;
    const body = await request.json();
    const { status, paymentStatus, trackingNumber, notes } = body;
    
    // Find the order
    const orderIndex = mockOrders.findIndex(o => o.id === orderId);
    
    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }
    
    // Update the order
    const updatedOrder = {
      ...mockOrders[orderIndex],
      ...(status && { status }),
      ...(paymentStatus && { paymentStatus }),
      ...(trackingNumber && { trackingNumber }),
      ...(notes && { notes }),
      updatedAt: new Date().toISOString()
    };
    
    mockOrders[orderIndex] = updatedOrder;
    
    return NextResponse.json(updatedOrder);
  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    );
  }
}

// DELETE /api/orders/[id] - Cancel an order
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const orderId = params.id;
    
    // Find the order
    const orderIndex = mockOrders.findIndex(o => o.id === orderId);
    
    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }
    
    // Check if order can be cancelled
    const order = mockOrders[orderIndex];
    if (order.status === 'delivered' || order.status === 'cancelled') {
      return NextResponse.json(
        { error: 'Cannot cancel this order' },
        { status: 400 }
      );
    }
    
    // Update order status to cancelled
    const cancelledOrder = {
      ...order,
      status: 'cancelled' as const,
      updatedAt: new Date().toISOString()
    };
    
    mockOrders[orderIndex] = cancelledOrder;
    
    return NextResponse.json(cancelledOrder);
  } catch (error) {
    console.error('Error cancelling order:', error);
    return NextResponse.json(
      { error: 'Failed to cancel order' },
      { status: 500 }
    );
  }
}
