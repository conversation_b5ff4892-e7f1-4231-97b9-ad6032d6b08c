import { NextRequest, NextResponse } from 'next/server';
import { Order, OrderItem } from '@/types';
import { db } from '@/firebase/admin';

// GET - Fetch all orders
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const status = searchParams.get('status');

    // Get orders collection reference
    let ordersRef = db.collection('orders');

    // Apply filters
    if (userId) {
      ordersRef = ordersRef.where('userId', '==', userId);
    }

    if (status) {
      ordersRef = ordersRef.where('status', '==', status);
    }

    // Note: Removed orderBy to avoid index requirement for now
    // ordersRef = ordersRef.orderBy('createdAt', 'desc');

    // Execute query
    const snapshot = await ordersRef.get();

    const orders: Order[] = [];
    snapshot.forEach((doc) => {
      orders.push({
        id: doc.id,
        ...doc.data()
      } as Order);
    });

    return NextResponse.json(orders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

// POST - Create new order
export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json();

    // Validate required fields
    if (!orderData.userId || !orderData.items || !orderData.total) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate stock availability for all items
    const stockValidationErrors: string[] = [];
    const stockUpdates: { productId: string; newStock: number }[] = [];

    for (const item of orderData.items) {
      const productDoc = await db.collection('products').doc(item.productId).get();

      if (!productDoc.exists) {
        stockValidationErrors.push(`Product ${item.product.name} no longer exists`);
        continue;
      }

      const currentProduct = productDoc.data();

      if (!currentProduct.isActive) {
        stockValidationErrors.push(`Product ${item.product.name} is no longer available`);
        continue;
      }

      if (currentProduct.stock < item.quantity) {
        stockValidationErrors.push(
          `Insufficient stock for ${item.product.name}. Available: ${currentProduct.stock}, Requested: ${item.quantity}`
        );
        continue;
      }

      // Prepare stock update
      stockUpdates.push({
        productId: item.productId,
        newStock: currentProduct.stock - item.quantity
      });
    }

    // If there are stock validation errors, return them
    if (stockValidationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Stock validation failed', details: stockValidationErrors },
        { status: 400 }
      );
    }

    // Create order with timestamps
    const newOrder: Omit<Order, 'id'> = {
      ...orderData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Use a batch to update stock and create order atomically
    const batch = db.batch();

    // Add order to batch
    const orderRef = db.collection('orders').doc();
    batch.set(orderRef, newOrder);

    // Add stock updates to batch
    for (const update of stockUpdates) {
      const productRef = db.collection('products').doc(update.productId);
      batch.update(productRef, {
        stock: update.newStock,
        updatedAt: new Date().toISOString()
      });
    }

    // Execute batch
    await batch.commit();

    // Return the created order with its ID
    const createdOrder: Order = {
      id: orderRef.id,
      ...newOrder
    };

    return NextResponse.json(createdOrder, { status: 201 });
  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}

// PUT - Update order (e.g., change status)
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    const orderData = await request.json();

    // Add updated timestamp
    const updatedOrder = {
      ...orderData,
      updatedAt: new Date().toISOString()
    };

    // Update order in Firestore
    await db.collection('orders').doc(id).update(updatedOrder);

    // Get updated order
    const updatedDoc = await db.collection('orders').doc(id).get();

    if (!updatedDoc.exists) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    const order: Order = {
      id: updatedDoc.id,
      ...updatedDoc.data()
    } as Order;

    return NextResponse.json(order);
  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    );
  }
}

// DELETE - Delete order
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Check if order exists
    const orderDoc = await db.collection('orders').doc(id).get();

    if (!orderDoc.exists) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Delete order from Firestore
    await db.collection('orders').doc(id).delete();

    return NextResponse.json(
      { message: 'Order deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting order:', error);
    return NextResponse.json(
      { error: 'Failed to delete order' },
      { status: 500 }
    );
  }
}