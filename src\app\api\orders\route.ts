import { NextRequest, NextResponse } from 'next/server';import { Order, OrderItem } from '@/types';import { db } from '@/firebase/admin';// GET - Fetch all ordersexport async function GET(request: NextRequest) {  try {    const { searchParams } = new URL(request.url);    const userId = searchParams.get('userId');    const status = searchParams.get('status');        // Get orders collection reference    let ordersRef = db.collection('orders');        // Apply filters    if (userId) {      ordersRef = ordersRef.where('userId', '==', userId);    }        if (status) {      ordersRef = ordersRef.where('status', '==', status);    }        // Order by creation date (newest first)    ordersRef = ordersRef.orderBy('createdAt', 'desc');        // Execute query    const snapshot = await ordersRef.get();        const orders: Order[] = [];    snapshot.forEach((doc) => {      orders.push({        id: doc.id,        ...doc.data()      } as Order);    });        return NextResponse.json(orders);  } catch (error) {    console.error('Error fetching orders:', error);    return NextResponse.json(      { error: 'Failed to fetch orders' },      { status: 500 }    );  }}// POST - Create new orderexport async function POST(request: NextRequest) {  try {    const orderData = await request.json();        // Validate required fields    if (!orderData.userId || !orderData.items || !orderData.total) {      return NextResponse.json(        { error: 'Missing required fields' },        { status: 400 }      );    }        // Create order with timestamps    const newOrder: Omit<Order, 'id'> = {      ...orderData,      createdAt: new Date().toISOString(),      updatedAt: new Date().toISOString()    };        // Add order to Firestore    const docRef = await db.collection('orders').add(newOrder);        // Return the created order with its ID    const createdOrder: Order = {      id: docRef.id,      ...newOrder    };        return NextResponse.json(createdOrder, { status: 201 });  } catch (error) {    console.error('Error creating order:', error);    return NextResponse.json(      { error: 'Failed to create order' },      { status: 500 }    );  }}// PUT - Update order (e.g., change status)export async function PUT(request: NextRequest) {  try {    const { searchParams } = new URL(request.url);    const id = searchParams.get('id');        if (!id) {      return NextResponse.json(        { error: 'Order ID is required' },        { status: 400 }      );    }        const orderData = await request.json();        // Add updated timestamp    const updatedOrder = {      ...orderData,      updatedAt: new Date().toISOString()    };        // Update order in Firestore    await db.collection('orders').doc(id).update(updatedOrder);        // Get updated order    const updatedDoc = await db.collection('orders').doc(id).get();        if (!updatedDoc.exists) {      return NextResponse.json(        { error: 'Order not found' },        { status: 404 }      );    }        const order: Order = {      id: updatedDoc.id,      ...updatedDoc.data()    } as Order;        return NextResponse.json(order);  } catch (error) {    console.error('Error updating order:', error);    return NextResponse.json(      { error: 'Failed to update order' },      { status: 500 }    );  }}// DELETE - Delete orderexport async function DELETE(request: NextRequest) {  try {    const { searchParams } = new URL(request.url);    const id = searchParams.get('id');        if (!id) {      return NextResponse.json(        { error: 'Order ID is required' },        { status: 400 }      );    }        // Check if order exists    const orderDoc = await db.collection('orders').doc(id).get();        if (!orderDoc.exists) {      return NextResponse.json(        { error: 'Order not found' },        { status: 404 }      );    }        // Delete order from Firestore    await db.collection('orders').doc(id).delete();        return NextResponse.json(      { message: 'Order deleted successfully' },      { status: 200 }    );  } catch (error) {    console.error('Error deleting order:', error);    return NextResponse.json(      { error: 'Failed to delete order' },      { status: 500 }    );  }}