import { NextRequest, NextResponse } from 'next/server';
import { Product } from '@/types';
import { db } from '@/firebase/admin';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const doc = await db.collection('products').doc(params.id).get();
    
    if (!doc.exists) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    const product: Product = {
      id: doc.id,
      ...doc.data()
    } as Product;

    return NextResponse.json(product);
  } catch (error) {
    console.error('Error getting product:', error);
    return NextResponse.json(
      { error: 'Failed to get product' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const contentType = request.headers.get('content-type');
    let productData: Partial<Product>;
    
    if (contentType?.includes('application/json')) {
      productData = await request.json();
    } else {
      // Handle FormData
      const formData = await request.formData();
      productData = {
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        price: parseFloat(formData.get('price') as string),
        originalPrice: formData.get('originalPrice') 
          ? parseFloat(formData.get('originalPrice') as string) 
          : undefined,
        category: formData.get('category') as string,
        subcategory: formData.get('subcategory') as string,
        brand: formData.get('brand') as string,
        stock: parseInt(formData.get('stock') as string),
        featured: formData.get('featured') === 'true',
        isActive: formData.get('isActive') !== 'false',
        tags: formData.get('tags') 
          ? (formData.get('tags') as string).split(',').map(tag => tag.trim())
          : [],
        weight: formData.get('weight') 
          ? parseFloat(formData.get('weight') as string)
          : undefined,
        images: formData.get('images') 
          ? JSON.parse(formData.get('images') as string)
          : [],
        specifications: formData.get('specifications')
          ? JSON.parse(formData.get('specifications') as string)
          : {},
        dimensions: formData.get('dimensions')
          ? JSON.parse(formData.get('dimensions') as string)
          : undefined
      };
    }
    
    // Add updated timestamp
    const updatedProduct = {
      ...productData,
      updatedAt: new Date().toISOString()
    };
    
    // Update product in Firestore
    await db.collection('products').doc(params.id).update(updatedProduct);
    
    // Get updated product
    const updatedDoc = await db.collection('products').doc(params.id).get();
    
    if (!updatedDoc.exists) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }
    
    const product: Product = {
      id: updatedDoc.id,
      ...updatedDoc.data()
    } as Product;
    
    return NextResponse.json(product);
  } catch (error) {
    console.error('Error updating product:', error);
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if product exists
    const productDoc = await db.collection('products').doc(params.id).get();
    
    if (!productDoc.exists) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }
    
    // Delete product from Firestore
    await db.collection('products').doc(params.id).delete();
    
    return NextResponse.json(
      { message: 'Product deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}
