import { NextRequest, NextResponse } from 'next/server';
import { getProductsByCategory } from '@/lib/actions/product.actions';

export async function GET(
  request: NextRequest,
  { params }: { params: { category: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const limitCount = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    const products = await getProductsByCategory(params.category, limitCount);

    return NextResponse.json(products);
  } catch (error) {
    console.error('Error getting products by category:', error);
    return NextResponse.json(
      { error: 'Failed to get products by category' },
      { status: 500 }
    );
  }
}
