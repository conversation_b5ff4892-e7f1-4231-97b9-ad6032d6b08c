import { NextRequest, NextResponse } from 'next/server';
import { getFeaturedProducts } from '@/lib/actions/product.actions';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limitCount = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    const products = await getFeaturedProducts(limitCount);

    return NextResponse.json(products);
  } catch (error) {
    console.error('Error getting featured products:', error);
    return NextResponse.json(
      { error: 'Failed to get featured products' },
      { status: 500 }
    );
  }
}
