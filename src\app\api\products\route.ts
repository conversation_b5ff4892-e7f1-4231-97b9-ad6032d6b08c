import { NextRequest, NextResponse } from 'next/server';import { Product } from '@/types';
import { db } from '@/firebase/admin';

// GET - Fetch all products
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const search = searchParams.get('search');
    
    // Get products collection reference
    let productsQuery: any = db.collection('products');
    
    // Apply filters
    if (category && category !== 'All') {
      productsQuery = productsQuery.where('category', '==', category);
    }
    
    if (featured === 'true') {
      productsQuery = productsQuery.where('featured', '==', true);
    }
    
    // Execute query
    const snapshot = await productsQuery.get();
    
    let products: Product[] = [];
    snapshot.forEach((doc: any) => {
      products.push({
        id: doc.id,
        ...doc.data()
      } as Product);
    });
    
    // Apply search filter (client-side for now)
    if (search) {
      const searchTerm = search.toLowerCase();
      products = products.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm) ||
        (product.brand && product.brand.toLowerCase().includes(searchTerm))
      );
    }
    
    return NextResponse.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// POST - Create new product
export async function POST(request: NextRequest) {
  try {
    const contentType = request.headers.get('content-type');
    let productData: Partial<Product>;
    let imageFiles: File[] = [];

    if (contentType?.includes('application/json')) {
      productData = await request.json();
    } else {
      // Handle FormData
      const formData = await request.formData();
      productData = {
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        price: parseFloat(formData.get('price') as string),
        originalPrice: formData.get('originalPrice') 
          ? parseFloat(formData.get('originalPrice') as string) 
          : undefined,
        category: formData.get('category') as string,
        subcategory: formData.get('subcategory') as string,
        brand: formData.get('brand') as string,
        stock: parseInt(formData.get('stock') as string),
        featured: formData.get('featured') === 'true',
        isActive: formData.get('isActive') !== 'false',
        tags: formData.get('tags') 
          ? (formData.get('tags') as string).split(',').map(tag => tag.trim())
          : [],
        weight: formData.get('weight') 
          ? parseFloat(formData.get('weight') as string)
          : undefined,
        specifications: formData.get('specifications')
          ? JSON.parse(formData.get('specifications') as string)
          : {},
        dimensions: formData.get('dimensions')
          ? JSON.parse(formData.get('dimensions') as string)
          : undefined
      };
      // Get image files from FormData
      const images = formData.getAll('images');
      imageFiles = images.filter(img => typeof img !== 'string') as File[];
    }

    // Validate required fields
    if (!productData.name || !productData.description || !productData.price || 
        !productData.category || !productData.brand || productData.stock === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Upload images to Cloudinary if any
    let uploadedImages: any[] = [];
    if (imageFiles.length > 0) {
      const { uploadToCloudinary } = await import('@/lib/cloudinary');
      const uploadPromises = imageFiles.map(async (file, index) => {
        const buffer = Buffer.from(await file.arrayBuffer());
        const uploadResult = await uploadToCloudinary(buffer, {
          folder: 'ecommerce-products',
          public_id: `${productData.name?.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}-${index}`,
        });
        return {
          id: `img_${Date.now()}_${index}`,
          url: uploadResult.secure_url,
          public_id: uploadResult.public_id,
          alt: `${productData.name} image ${index + 1}`,
          isPrimary: index === 0,
        };
      });
      uploadedImages = await Promise.all(uploadPromises);
    }

    // Create product with timestamps
    const newProduct: Omit<Product, 'id'> = {
      ...productData as Product,
      images: uploadedImages,
      rating: 0,
      reviewCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add product to Firestore
    const docRef = await db.collection('products').add(newProduct);

    // Return the created product with its ID
    const createdProduct: Product = {
      id: docRef.id,
      ...newProduct
    };

    return NextResponse.json(createdProduct, { status: 201 });
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}

// PUT - Update product
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }
    
    const contentType = request.headers.get('content-type');
    let productData: Partial<Product>;
    let hasNewImages = false;
    let newImages: any[] = [];
    if (contentType?.includes('application/json')) {
      productData = await request.json();
      if (productData.images && Array.isArray(productData.images) && productData.images.length > 0) {
        hasNewImages = true;
        newImages = productData.images;
      }
    } else {
      // Handle FormData
      const formData = await request.formData();
      productData = {
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        price: parseFloat(formData.get('price') as string),
        originalPrice: formData.get('originalPrice') 
          ? parseFloat(formData.get('originalPrice') as string) 
          : undefined,
        category: formData.get('category') as string,
        subcategory: formData.get('subcategory') as string,
        brand: formData.get('brand') as string,
        stock: parseInt(formData.get('stock') as string),
        featured: formData.get('featured') === 'true',
        isActive: formData.get('isActive') !== 'false',
        tags: formData.get('tags') 
          ? (formData.get('tags') as string).split(',').map(tag => tag.trim())
          : [],
        weight: formData.get('weight') 
          ? parseFloat(formData.get('weight') as string)
          : undefined,
        specifications: formData.get('specifications')
          ? JSON.parse(formData.get('specifications') as string)
          : {},
        dimensions: formData.get('dimensions')
          ? JSON.parse(formData.get('dimensions') as string)
          : undefined
      };
      // Only set images if present in FormData
      if (formData.get('images')) {
        hasNewImages = true;
        newImages = JSON.parse(formData.get('images') as string);
      }
    }
    // If no new images, fetch previous images from Firestore
    if (!hasNewImages) {
      const prevDoc = await db.collection('products').doc(id).get();
      if (prevDoc.exists) {
        const prevData = prevDoc.data();
        productData.images = prevData && prevData.images ? prevData.images : [];
      }
    } else {
      productData.images = newImages;
    }
    // Add updated timestamp
    const updatedProduct = {
      ...productData,
      updatedAt: new Date().toISOString()
    };
    // Update product in Firestore
    await db.collection('products').doc(id).update(updatedProduct);
    // Get updated product
    const updatedDoc = await db.collection('products').doc(id).get();
    if (!updatedDoc.exists) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }
    const product: Product = {
      id: updatedDoc.id,
      ...updatedDoc.data()
    } as Product;
    return NextResponse.json(product);
  } catch (error) {
    console.error('Error updating product:', error);
    return NextResponse.json(
      { error: 'Failed to update product' },
      { status: 500 }
    );
  }
}

// DELETE - Delete product
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }
    
    // Check if product exists
    const productDoc = await db.collection('products').doc(id).get();
    
    if (!productDoc.exists) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }
    
    // Delete product from Firestore
    await db.collection('products').doc(id).delete();
    
    return NextResponse.json(
      { message: 'Product deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}
