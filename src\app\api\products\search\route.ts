import { NextRequest, NextResponse } from 'next/server';
import { searchProducts } from '@/lib/actions/product.actions';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get('q');
    const category = searchParams.get('category') || undefined;
    const limitCount = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    if (!searchTerm) {
      return NextResponse.json(
        { error: 'Search term is required' },
        { status: 400 }
      );
    }

    const products = await searchProducts(searchTerm, {
      category,
      limitCount,
    });

    return NextResponse.json(products);
  } catch (error) {
    console.error('Error searching products:', error);
    return NextResponse.json(
      { error: 'Failed to search products' },
      { status: 500 }
    );
  }
}
