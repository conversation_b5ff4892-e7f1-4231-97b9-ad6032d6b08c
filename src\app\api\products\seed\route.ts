import { NextRequest, NextResponse } from 'next/server';
import { seedDatabase } from '@/lib/seed-database';

export async function POST(request: NextRequest) {
  try {
    const result = await seedDatabase();
    
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error('Error seeding database:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to seed database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
