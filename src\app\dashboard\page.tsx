"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import AdminRestrictionGuard from '@/components/AdminRestrictionGuard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Package, 
  ShoppingCart, 
  Heart, 
  User, 
  Settings,
  Bell,
  CreditCard,
  MapPin,
  Clock,
  Star,
  Gift,
  Truck,
  Shield,
  Eye,
  MessageSquare,
  TrendingUp,
  Activity,
  DollarSign
} from 'lucide-react';
import { Order } from '@/types';
import Link from 'next/link';
import RoleGuard from '@/components/RoleGuard';

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.3 }
};

const stagger = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function CustomerDashboard() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalSpent: 0,
    pendingOrders: 0,
    completedOrders: 0,
    favoriteProducts: 0,
    rewardPoints: 0
  });

  // Fetch user orders and stats
  useEffect(() => {
    if (user) {
      fetchUserData();
    }
  }, [user]);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      
      // Fetch user orders
      const ordersResponse = await fetch('/api/orders');
      const ordersData = await ordersResponse.json();
      
      // Filter orders for current user (in a real app, this would be server-side)
      const userOrders = ordersData.filter((order: Order) => order.userId === user?.id);
      setOrders(userOrders);

      // Calculate stats
      const totalSpent = userOrders.reduce((sum: number, order: Order) => sum + order.total, 0);
      const pendingOrders = userOrders.filter((order: Order) => order.status === 'pending').length;
      const completedOrders = userOrders.filter((order: Order) => order.status === 'delivered').length;

      setStats({
        totalOrders: userOrders.length,
        totalSpent,
        pendingOrders,
        completedOrders,
        favoriteProducts: 12, // Mock data
        rewardPoints: Math.floor(totalSpent / 10) // 1 point per $10 spent
      });
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Activity },
    { id: 'orders', label: 'My Orders', icon: Package },
    { id: 'favorites', label: 'Favorites', icon: Heart },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'addresses', label: 'Addresses', icon: MapPin },
    { id: 'payments', label: 'Payment Methods', icon: CreditCard },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const StatCard = ({ title, value, icon: Icon, color, subtitle }: {
    title: string;
    value: string | number;
    icon: any;
    color: string;
    subtitle?: string;
  }) => (
    <motion.div variants={fadeInUp}>
      <Card className="hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
              {subtitle && (
                <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
              )}
            </div>
            <div className={`p-3 rounded-full ${color}`}>
              <Icon className="h-6 w-6 text-white" />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AdminRestrictionGuard>
      <RoleGuard requiredRole="customer">
      <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
      {/* Navigation */}
      <div className="border-b bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold">My Account</h1>
              <Badge variant="outline" className="bg-blue-50 text-blue-600">
                {user?.name}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <MessageSquare className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">My Account</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {tabItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-100 transition-colors ${
                        activeTab === item.id ? 'bg-primary/10 text-primary border-r-2 border-primary' : ''
                      }`}
                    >
                      <item.icon className="h-4 w-4" />
                      {item.label}
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeTab === 'dashboard' && (
              <div className="space-y-8">
                {/* Welcome Message */}
                <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-2xl font-bold mb-2">Welcome back, {user?.name}!</h2>
                        <p className="text-blue-100">
                          You have {stats.rewardPoints} reward points to spend
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-blue-100">Member since</p>
                        <p className="font-semibold">January 2024</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Stats Cards */}
                {loading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array(6).fill(0).map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-6">
                          <div className="h-16 bg-gray-200 rounded"></div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <motion.div
                    initial="initial"
                    animate="animate"
                    variants={stagger}
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                  >
                    <StatCard
                      title="Total Orders"
                      value={stats.totalOrders}
                      icon={Package}
                      color="bg-blue-500"
                      subtitle="All time orders"
                    />
                    <StatCard
                      title="Total Spent"
                      value={`$${stats.totalSpent.toFixed(2)}`}
                      icon={DollarSign}
                      color="bg-green-500"
                      subtitle="Lifetime spending"
                    />
                    <StatCard
                      title="Pending Orders"
                      value={stats.pendingOrders}
                      icon={Clock}
                      color="bg-yellow-500"
                      subtitle="Orders in progress"
                    />
                    <StatCard
                      title="Completed Orders"
                      value={stats.completedOrders}
                      icon={Shield}
                      color="bg-purple-500"
                      subtitle="Successfully delivered"
                    />
                    <StatCard
                      title="Favorite Products"
                      value={stats.favoriteProducts}
                      icon={Heart}
                      color="bg-red-500"
                      subtitle="Items in wishlist"
                    />
                    <StatCard
                      title="Reward Points"
                      value={stats.rewardPoints}
                      icon={Gift}
                      color="bg-orange-500"
                      subtitle="Available to spend"
                    />
                  </motion.div>
                )}

                {/* Quick Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <Link href="/products">
                        <Button 
                          className="w-full justify-start h-auto p-4"
                          variant="outline"
                        >
                          <div className="text-left">
                            <p className="font-medium">Shop Products</p>
                            <p className="text-sm text-gray-600">Browse our latest collection</p>
                          </div>
                        </Button>
                      </Link>
                      <Button 
                        onClick={() => setActiveTab('orders')}
                        className="justify-start h-auto p-4"
                        variant="outline"
                      >
                        <div className="text-left">
                          <p className="font-medium">Track Orders</p>
                          <p className="text-sm text-gray-600">Check your order status</p>
                        </div>
                      </Button>
                      <Link href="/contact">
                        <Button 
                          className="w-full justify-start h-auto p-4"
                          variant="outline"
                        >
                          <div className="text-left">
                            <p className="font-medium">Contact Support</p>
                            <p className="text-sm text-gray-600">Get help with your orders</p>
                          </div>
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Orders */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Package className="h-5 w-5" />
                      Recent Orders
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {orders.length === 0 ? (
                      <div className="text-center py-8">
                        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">No orders yet</p>
                        <Link href="/products">
                          <Button className="mt-4">Start Shopping</Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {orders.slice(0, 3).map((order) => (
                          <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="flex items-center gap-4">
                              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Package className="h-6 w-6 text-blue-600" />
                              </div>
                              <div>
                                <p className="font-medium">Order #{order.id}</p>
                                <p className="text-sm text-gray-600">{order.items.length} items</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">${order.total.toFixed(2)}</p>
                              <Badge className={getStatusColor(order.status)}>
                                {order.status}
                              </Badge>
                            </div>
                          </div>
                        ))}
                        {orders.length > 3 && (
                          <Button 
                            variant="outline" 
                            className="w-full"
                            onClick={() => setActiveTab('orders')}
                          >
                            View All Orders
                          </Button>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'orders' && (
              <Card>
                <CardHeader>
                  <CardTitle>My Orders</CardTitle>
                  <CardDescription>Track and manage your orders</CardDescription>
                </CardHeader>
                <CardContent>
                  {orders.length === 0 ? (
                    <div className="text-center py-8">
                      <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">You haven't placed any orders yet</p>
                      <Link href="/products">
                        <Button>Start Shopping</Button>
                      </Link>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {orders.map((order) => (
                        <div key={order.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-4">
                            <div>
                              <h3 className="font-medium">Order #{order.id}</h3>
                              <p className="text-sm text-gray-600">
                                Placed on {new Date(order.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">${order.total.toFixed(2)}</p>
                              <Badge className={getStatusColor(order.status)}>
                                {order.status}
                              </Badge>
                            </div>
                          </div>
                          <div className="space-y-2">
                            {order.items.map((item, index) => (
                              <div key={index} className="flex items-center gap-3 text-sm">
                                <div className="w-8 h-8 bg-gray-100 rounded"></div>
                                <span>{item.product.name} x{item.quantity}</span>
                                <span className="ml-auto">${(item.price * item.quantity).toFixed(2)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
            
            {activeTab === 'favorites' && (
              <div className="text-center py-12">
                <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Your Favorites</h3>
                <p className="text-gray-600">Save products you love for later</p>
              </div>
            )}
            
            {activeTab === 'profile' && (
              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>Manage your personal information</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Full Name</label>
                        <p className="text-gray-900">{user?.name}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Email</label>
                        <p className="text-gray-900">{user?.email}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Phone</label>
                        <p className="text-gray-900">Not provided</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Member Since</label>
                        <p className="text-gray-900">January 2024</p>
                      </div>
                    </div>
                    <Button>Edit Profile</Button>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {activeTab === 'addresses' && (
              <div className="text-center py-12">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Shipping Addresses</h3>
                <p className="text-gray-600">Manage your delivery addresses</p>
              </div>
            )}
            
            {activeTab === 'payments' && (
              <div className="text-center py-12">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Payment Methods</h3>
                <p className="text-gray-600">Manage your payment options</p>
              </div>
            )}
            
            {activeTab === 'settings' && (
              <div className="text-center py-12">
                <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Account Settings</h3>
                <p className="text-gray-600">Manage your account preferences</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    </RoleGuard>
    </AdminRestrictionGuard>
  );
}
