"use client"

import { motion } from "framer-motion"
import { ShoppingBag, Shield, Truck, Star, ArrowRight, Sparkles, TrendingUp, Heart, ShoppingCart, Settings, BarChart3 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { useAuth } from "@/contexts/AuthContext"

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
}

const stagger = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

const features = [
  {
    icon: ShoppingBag,
    title: "Easy Shopping",
    description: "Browse thousands of products with our intuitive interface",
    gradient: "from-blue-500 to-purple-600"
  },
  {
    icon: Shield,
    title: "Secure Payments",
    description: "Your transactions are protected with enterprise-grade security",
    gradient: "from-green-500 to-teal-600"
  },
  {
    icon: Truck,
    title: "Fast Delivery",
    description: "Get your orders delivered quickly to your doorstep",
    gradient: "from-orange-500 to-red-600"
  },
  {
    icon: Star,
    title: "Premium Quality",
    description: "Every product is carefully selected for the best quality",
    gradient: "from-yellow-500 to-orange-600"
  }
]

const featuredProducts = [
  {
    id: 1,
    name: "Premium Headphones",
    price: 299.99,
    originalPrice: 399.99,
    image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
    badge: "Best Seller",
    rating: 4.8,
    reviews: 1234,
    isNew: false
  },
  {
    id: 2,
    name: "Smart Watch",
    price: 199.99,
    originalPrice: 249.99,
    image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=300&fit=crop",
    badge: "New",
    rating: 4.9,
    reviews: 856,
    isNew: true
  },
  {
    id: 3,
    name: "Wireless Earbuds",
    price: 89.99,
    originalPrice: 129.99,
    image: "https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=300&h=300&fit=crop",
    badge: "Limited",
    rating: 4.7,
    reviews: 2341,
    isNew: false
  },
  {
    id: 4,
    name: "Gaming Keyboard",
    price: 159.99,
    originalPrice: 199.99,
    image: "https://images.unsplash.com/photo-1587829741301-dc798b83add3?w=300&h=300&fit=crop",
    badge: "Trending",
    rating: 4.6,
    reviews: 987,
    isNew: false
  }
]

const stats = [
  { value: "50K+", label: "Happy Customers" },
  { value: "10K+", label: "Products" },
  { value: "99.9%", label: "Uptime" },
  { value: "24/7", label: "Support" }
]

export default function Home() {
  const { user } = useAuth();
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/10 to-pink-600/10" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(120,119,198,0.3),transparent_50%)]" />
        <div className="container mx-auto px-4 py-20 lg:py-32 relative">
          <motion.div
            initial="initial"
            animate="animate"
            variants={stagger}
            className="text-center space-y-8"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge variant="outline" className="mx-auto flex items-center gap-2 w-fit px-4 py-2">
                <Sparkles className="h-4 w-4" />
                New Collection Available
              </Badge>
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent leading-tight">
                Shop the Future
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Discover premium products with cutting-edge technology and exceptional quality that transform your everyday life
              </p>
            </motion.div>
            
            <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="xl" variant="gradient">
                <Link href="/products" className="flex items-center gap-2">
                  Shop Now
                  <ArrowRight className="h-5 w-5" />
                </Link>
              </Button>
              <Button asChild size="xl" variant="outline">
                <Link href="/about">Learn More</Link>
              </Button>
            </motion.div>

            {/* Stats */}
            <motion.div variants={fadeInUp} className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-primary">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Quick Access Section for logged in users */}
      {user && (
        <section className="py-16 bg-white/50">
          <div className="container mx-auto px-4">
            <motion.div
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              variants={stagger}
              className="space-y-8"
            >
              <motion.div variants={fadeInUp} className="text-center space-y-4">
                <h2 className="text-3xl md:text-4xl font-bold">Welcome back, {user.name}!</h2>
                <p className="text-lg text-muted-foreground">
                  {user.role === 'admin' ? 'Manage your store with powerful admin tools' : 'Continue your shopping journey'}
                </p>
              </motion.div>
              
              <motion.div variants={stagger} className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
                {user.role === 'admin' ? (
                  // Admin Quick Access
                  <>
                    <motion.div variants={fadeInUp}>
                      <Card className="group hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <BarChart3 className="h-8 w-8 text-white" />
                          </div>
                          <CardTitle>Admin Dashboard</CardTitle>
                        </CardHeader>
                        <CardContent className="text-center">
                          <CardDescription className="mb-4">
                            View analytics, manage products, and monitor your store
                          </CardDescription>
                          <Link href="/admin">
                            <Button className="w-full">Go to Dashboard</Button>
                          </Link>
                        </CardContent>
                      </Card>
                    </motion.div>
                    
                    <motion.div variants={fadeInUp}>
                      <Card className="group hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <ShoppingBag className="h-8 w-8 text-white" />
                          </div>
                          <CardTitle>Manage Products</CardTitle>
                        </CardHeader>
                        <CardContent className="text-center">
                          <CardDescription className="mb-4">
                            Add, edit, and organize your product catalog
                          </CardDescription>
                          <Link href="/admin">
                            <Button className="w-full" variant="outline">Manage Products</Button>
                          </Link>
                        </CardContent>
                      </Card>
                    </motion.div>
                    
                    <motion.div variants={fadeInUp}>
                      <Card className="group hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <Truck className="h-8 w-8 text-white" />
                          </div>
                          <CardTitle>Orders & Customers</CardTitle>
                        </CardHeader>
                        <CardContent className="text-center">
                          <CardDescription className="mb-4">
                            Process orders and communicate with customers
                          </CardDescription>
                          <Link href="/admin">
                            <Button className="w-full" variant="outline">View Orders</Button>
                          </Link>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </>
                ) : (
                  // Customer Quick Access
                  <>
                    <motion.div variants={fadeInUp}>
                      <Card className="group hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <Settings className="h-8 w-8 text-white" />
                          </div>
                          <CardTitle>My Dashboard</CardTitle>
                        </CardHeader>
                        <CardContent className="text-center">
                          <CardDescription className="mb-4">
                            View your orders, profile, and account settings
                          </CardDescription>
                          <Link href="/dashboard">
                            <Button className="w-full">Go to Dashboard</Button>
                          </Link>
                        </CardContent>
                      </Card>
                    </motion.div>
                    
                    <motion.div variants={fadeInUp}>
                      <Card className="group hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <ShoppingCart className="h-8 w-8 text-white" />
                          </div>
                          <CardTitle>Continue Shopping</CardTitle>
                        </CardHeader>
                        <CardContent className="text-center">
                          <CardDescription className="mb-4">
                            Browse our latest products and add to cart
                          </CardDescription>
                          <Link href="/products">
                            <Button className="w-full" variant="outline">Shop Now</Button>
                          </Link>
                        </CardContent>
                      </Card>
                    </motion.div>
                    
                    <motion.div variants={fadeInUp}>
                      <Card className="group hover:shadow-lg transition-all duration-300">
                        <CardHeader className="text-center">
                          <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                            <Heart className="h-8 w-8 text-white" />
                          </div>
                          <CardTitle>Your Cart</CardTitle>
                        </CardHeader>
                        <CardContent className="text-center">
                          <CardDescription className="mb-4">
                            View and manage items in your shopping cart
                          </CardDescription>
                          <Link href="/cart">
                            <Button className="w-full" variant="outline">View Cart</Button>
                          </Link>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </>
                )}
              </motion.div>
            </motion.div>
          </div>
        </section>
      )}

      {/* Features Section */}
      <section className="py-20 bg-background/50">
        <div className="container mx-auto px-4">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={stagger}
            className="space-y-12"
          >
            <motion.div variants={fadeInUp} className="text-center space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold">Why Choose Us</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                We provide exceptional service and quality products that exceed your expectations
              </p>
            </motion.div>
            
            <motion.div variants={stagger} className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {features.map((feature, index) => (
                <motion.div key={index} variants={fadeInUp}>
                  <Card className="relative overflow-hidden group hover:scale-105 transition-all duration-300 border-0 shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-br from-background to-secondary/30" />
                    <CardHeader className="text-center relative">
                      <div className={`w-16 h-16 mx-auto rounded-full bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                        <feature.icon className="h-8 w-8 text-white" />
                      </div>
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="relative">
                      <CardDescription className="text-center">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={stagger}
            className="space-y-12"
          >
            <motion.div variants={fadeInUp} className="text-center space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold">Featured Products</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Handpicked products that are trending and loved by our customers
              </p>
            </motion.div>
            
            <motion.div variants={stagger} className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredProducts.map((product) => (
                <motion.div key={product.id} variants={fadeInUp}>
                  <Card className="group cursor-pointer hover:scale-105 transition-all duration-300 overflow-hidden border-0 shadow-lg">
                    <div className="relative">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <Badge 
                        className={`absolute top-4 left-4 ${product.isNew ? 'bg-green-500' : 'bg-blue-500'} text-white`}
                      >
                        {product.badge}
                      </Badge>
                      <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1 text-white text-sm flex items-center gap-1">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        {product.rating}
                      </div>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="absolute top-4 right-12 bg-black/50 backdrop-blur-sm text-white hover:bg-black/70 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg leading-tight">{product.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-primary">${product.price}</span>
                        {product.originalPrice && (
                          <span className="text-sm text-muted-foreground line-through">
                            ${product.originalPrice}
                          </span>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex items-center justify-between mb-4">
                        <span className="text-sm text-muted-foreground">
                          {product.reviews} reviews
                        </span>
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-green-500">Popular</span>
                        </div>
                      </div>
                      <Button className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Add to Cart
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
            
            <motion.div variants={fadeInUp} className="text-center">
              <Button asChild size="lg" variant="gradient">
                <Link href="/products" className="flex items-center gap-2">
                  View All Products
                  <ArrowRight className="h-5 w-5" />
                </Link>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>
      
      {/* Newsletter Section */}
      <section className="py-20 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
        <div className="container mx-auto px-4">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="max-w-2xl mx-auto text-center space-y-8"
          >
            <div className="space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold">Stay Updated</h2>
              <p className="text-lg text-muted-foreground">
                Subscribe to our newsletter and be the first to know about new products, exclusive offers, and special deals
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg border bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <Button variant="gradient" size="lg">
                Subscribe
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center space-y-8 text-white"
          >
            <h2 className="text-3xl md:text-4xl font-bold">
              Ready to Start Shopping?
            </h2>
            <p className="text-xl opacity-90 max-w-2xl mx-auto">
              Join thousands of satisfied customers and discover amazing products at unbeatable prices
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="xl" variant="secondary">
                <Link href="/signup">Create Account</Link>
              </Button>
              <Button asChild size="xl" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600">
                <Link href="/products">Browse Products</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
