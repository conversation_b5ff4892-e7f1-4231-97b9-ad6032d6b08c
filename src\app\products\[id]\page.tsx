import ProductDetailComponent from '@/components/ProductDetailComponent';
import AdminRestrictionGuard from '@/components/AdminRestrictionGuard';

interface ProductDetailPageProps {
  params: {
    id: string;
  };
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  return (
    <AdminRestrictionGuard>
      <ProductDetailComponent productId={params.id} />
    </AdminRestrictionGuard>
  );
}
