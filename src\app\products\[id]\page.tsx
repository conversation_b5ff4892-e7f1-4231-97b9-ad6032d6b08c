import ProductDetailComponent from '@/components/ProductDetailComponent';
import AdminRestrictionGuard from '@/components/AdminRestrictionGuard';

interface ProductDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ProductDetailPage({ params }: ProductDetailPageProps) {
  const { id } = await params;

  return (
    <AdminRestrictionGuard>
      <ProductDetailComponent productId={id} />
    </AdminRestrictionGuard>
  );
}
