"use client";

import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface AdminRestrictionGuardProps {
  children: React.ReactNode;
  redirectPath?: string;
}

export default function AdminRestrictionGuard({ 
  children, 
  redirectPath = '/admin' 
}: AdminRestrictionGuardProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user && user.role === 'admin') {
      // Admin users should not access this page, redirect to admin dashboard
      router.push(redirectPath);
    }
  }, [user, loading, redirectPath, router]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If user is admin, show nothing (they will be redirected)
  if (user && user.role === 'admin') {
    return null;
  }

  // For non-admin users or unauthenticated users, show the content
  return <>{children}</>;
}
