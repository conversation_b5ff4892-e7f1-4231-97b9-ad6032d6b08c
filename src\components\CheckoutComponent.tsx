"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CreditCard, 
  MapPin, 
  User, 
  Phone, 
  Mail,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Package,
  Truck,
  Shield
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Cart, Address, Order } from '@/types';
import { useAuth } from '@/contexts/AuthContext';

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.3 }
};

export default function CheckoutComponent() {
  const { user } = useAuth();
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [orderPlaced, setOrderPlaced] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);

  // Form data
  const [shippingAddress, setShippingAddress] = useState<Address>({
    firstName: '',
    lastName: '',
    street: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    phone: ''
  });

  const [billingAddress, setBillingAddress] = useState<Address>({
    firstName: '',
    lastName: '',
    street: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'United States',
    phone: ''
  });

  const [paymentMethod, setPaymentMethod] = useState<'credit_card' | 'paypal' | 'bank_transfer' | 'cash_on_delivery'>('credit_card');
  const [sameAsShipping, setSameAsShipping] = useState(true);

  // Fetch cart data
  useEffect(() => {
    if (user) {
      fetchCart();
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchCart = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`/api/cart?userId=${user.uid}`);
      
      if (response.ok) {
        const cartData = await response.json();
        setCart(cartData);
      } else {
        setError('Failed to fetch cart');
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
      setError('Failed to fetch cart');
    } finally {
      setLoading(false);
    }
  };

  // Calculate totals
  const cartItems = cart?.items || [];
  const subtotal = cart?.total || 0;
  const tax = subtotal * 0.08; // 8% tax
  const shipping = subtotal > 100 ? 0 : 9.99; // Free shipping over $100
  const total = subtotal + tax + shipping;

  // Validation functions
  const validateAddress = (address: Address): boolean => {
    return !!(
      address.firstName &&
      address.lastName &&
      address.street &&
      address.city &&
      address.state &&
      address.zipCode &&
      address.country
    );
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return validateAddress(shippingAddress);
      case 2:
        return sameAsShipping || validateAddress(billingAddress);
      case 3:
        return !!paymentMethod;
      default:
        return false;
    }
  };

  // Handle step navigation
  const nextStep = () => {
    if (validateStep(currentStep) && currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle order placement
  const placeOrder = async () => {
    if (!user || !cart || cartItems.length === 0) return;

    try {
      setSubmitting(true);
      setError(null);

      const orderData = {
        userId: user.uid,
        user: {
          id: user.uid,
          email: user.email,
          name: user.displayName || `${shippingAddress.firstName} ${shippingAddress.lastName}`,
          role: 'customer'
        },
        items: cartItems.map(item => ({
          id: item.id,
          productId: item.productId,
          product: item.product,
          quantity: item.quantity,
          price: item.price,
          subtotal: item.price * item.quantity
        })),
        subtotal,
        tax,
        shipping,
        total,
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod,
        shippingAddress,
        billingAddress: sameAsShipping ? shippingAddress : billingAddress,
        notes: ''
      };

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        const order = await response.json();
        setOrderId(order.id);
        setOrderPlaced(true);
        
        // Clear the cart
        await fetch(`/api/cart?userId=${user.uid}`, {
          method: 'DELETE'
        });
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to place order');
      }
    } catch (error) {
      console.error('Error placing order:', error);
      setError('Failed to place order');
    } finally {
      setSubmitting(false);
    }
  };

  // Show login message if user is not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center py-12">
            <Package className="h-24 w-24 text-gray-400 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold mb-4">Please log in to checkout</h2>
            <p className="text-gray-600 mb-8">You need to be logged in to place an order.</p>
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              Log In
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading checkout...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show empty cart message
  if (!cart || cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center py-12">
            <Package className="h-24 w-24 text-gray-400 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold mb-4">Your cart is empty</h2>
            <p className="text-gray-600 mb-8">Add some products to your cart before checkout.</p>
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              Continue Shopping
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show order confirmation
  if (orderPlaced) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
        <div className="container mx-auto px-4 py-12">
          <motion.div
            initial="initial"
            animate="animate"
            variants={fadeInUp}
            className="max-w-2xl mx-auto text-center"
          >
            <CheckCircle className="h-24 w-24 text-green-500 mx-auto mb-6" />
            <h1 className="text-4xl font-bold mb-4">Order Placed Successfully!</h1>
            <p className="text-gray-600 mb-8">
              Thank you for your order. Your order ID is <strong>#{orderId}</strong>
            </p>
            <div className="space-y-4">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                View Order Details
              </Button>
              <Button variant="outline" size="lg">
                Continue Shopping
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-secondary/20">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Checkout</h1>
          <p className="text-gray-600">Complete your order in a few simple steps</p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded max-w-4xl mx-auto">
            {error}
          </div>
        )}

        {/* Progress Steps */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex items-center justify-between">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`
                  w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium
                  ${currentStep >= step 
                    ? 'bg-primary text-white' 
                    : 'bg-gray-200 text-gray-600'
                  }
                `}>
                  {step}
                </div>
                {step < 4 && (
                  <div className={`
                    w-20 h-1 mx-2
                    ${currentStep > step ? 'bg-primary' : 'bg-gray-200'}
                  `} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span>Shipping</span>
            <span>Billing</span>
            <span>Payment</span>
            <span>Review</span>
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-6">
                {/* Step 1: Shipping Address */}
                {currentStep === 1 && (
                  <motion.div
                    initial="initial"
                    animate="animate"
                    variants={fadeInUp}
                    className="space-y-6"
                  >
                    <div className="flex items-center gap-2 mb-6">
                      <MapPin className="h-5 w-5 text-primary" />
                      <h2 className="text-xl font-semibold">Shipping Address</h2>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">First Name *</Label>
                        <Input
                          id="firstName"
                          value={shippingAddress.firstName}
                          onChange={(e) => setShippingAddress({...shippingAddress, firstName: e.target.value})}
                          placeholder="Enter first name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName">Last Name *</Label>
                        <Input
                          id="lastName"
                          value={shippingAddress.lastName}
                          onChange={(e) => setShippingAddress({...shippingAddress, lastName: e.target.value})}
                          placeholder="Enter last name"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="street">Street Address *</Label>
                      <Input
                        id="street"
                        value={shippingAddress.street}
                        onChange={(e) => setShippingAddress({...shippingAddress, street: e.target.value})}
                        placeholder="Enter street address"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="city">City *</Label>
                        <Input
                          id="city"
                          value={shippingAddress.city}
                          onChange={(e) => setShippingAddress({...shippingAddress, city: e.target.value})}
                          placeholder="Enter city"
                        />
                      </div>
                      <div>
                        <Label htmlFor="state">State *</Label>
                        <Input
                          id="state"
                          value={shippingAddress.state}
                          onChange={(e) => setShippingAddress({...shippingAddress, state: e.target.value})}
                          placeholder="Enter state"
                        />
                      </div>
                      <div>
                        <Label htmlFor="zipCode">ZIP Code *</Label>
                        <Input
                          id="zipCode"
                          value={shippingAddress.zipCode}
                          onChange={(e) => setShippingAddress({...shippingAddress, zipCode: e.target.value})}
                          placeholder="Enter ZIP code"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={shippingAddress.phone}
                        onChange={(e) => setShippingAddress({...shippingAddress, phone: e.target.value})}
                        placeholder="Enter phone number"
                      />
                    </div>
                  </motion.div>
                )}

                {/* Step 2: Billing Address */}
                {currentStep === 2 && (
                  <motion.div
                    initial="initial"
                    animate="animate"
                    variants={fadeInUp}
                    className="space-y-6"
                  >
                    <div className="flex items-center gap-2 mb-6">
                      <CreditCard className="h-5 w-5 text-primary" />
                      <h2 className="text-xl font-semibold">Billing Address</h2>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="sameAsShipping"
                        checked={sameAsShipping}
                        onChange={(e) => setSameAsShipping(e.target.checked)}
                        className="rounded"
                      />
                      <Label htmlFor="sameAsShipping">Same as shipping address</Label>
                    </div>

                    {!sameAsShipping && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="billingFirstName">First Name *</Label>
                            <Input
                              id="billingFirstName"
                              value={billingAddress.firstName}
                              onChange={(e) => setBillingAddress({...billingAddress, firstName: e.target.value})}
                              placeholder="Enter first name"
                            />
                          </div>
                          <div>
                            <Label htmlFor="billingLastName">Last Name *</Label>
                            <Input
                              id="billingLastName"
                              value={billingAddress.lastName}
                              onChange={(e) => setBillingAddress({...billingAddress, lastName: e.target.value})}
                              placeholder="Enter last name"
                            />
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="billingStreet">Street Address *</Label>
                          <Input
                            id="billingStreet"
                            value={billingAddress.street}
                            onChange={(e) => setBillingAddress({...billingAddress, street: e.target.value})}
                            placeholder="Enter street address"
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <Label htmlFor="billingCity">City *</Label>
                            <Input
                              id="billingCity"
                              value={billingAddress.city}
                              onChange={(e) => setBillingAddress({...billingAddress, city: e.target.value})}
                              placeholder="Enter city"
                            />
                          </div>
                          <div>
                            <Label htmlFor="billingState">State *</Label>
                            <Input
                              id="billingState"
                              value={billingAddress.state}
                              onChange={(e) => setBillingAddress({...billingAddress, state: e.target.value})}
                              placeholder="Enter state"
                            />
                          </div>
                          <div>
                            <Label htmlFor="billingZipCode">ZIP Code *</Label>
                            <Input
                              id="billingZipCode"
                              value={billingAddress.zipCode}
                              onChange={(e) => setBillingAddress({...billingAddress, zipCode: e.target.value})}
                              placeholder="Enter ZIP code"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}

                {/* Step 3: Payment Method */}
                {currentStep === 3 && (
                  <motion.div
                    initial="initial"
                    animate="animate"
                    variants={fadeInUp}
                    className="space-y-6"
                  >
                    <div className="flex items-center gap-2 mb-6">
                      <CreditCard className="h-5 w-5 text-primary" />
                      <h2 className="text-xl font-semibold">Payment Method</h2>
                    </div>

                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div
                          className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                            paymentMethod === 'credit_card' ? 'border-primary bg-primary/5' : 'border-gray-200'
                          }`}
                          onClick={() => setPaymentMethod('credit_card')}
                        >
                          <div className="flex items-center gap-3">
                            <CreditCard className="h-5 w-5" />
                            <div>
                              <p className="font-medium">Credit Card</p>
                              <p className="text-sm text-gray-600">Visa, Mastercard, American Express</p>
                            </div>
                          </div>
                        </div>

                        <div
                          className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                            paymentMethod === 'paypal' ? 'border-primary bg-primary/5' : 'border-gray-200'
                          }`}
                          onClick={() => setPaymentMethod('paypal')}
                        >
                          <div className="flex items-center gap-3">
                            <Shield className="h-5 w-5" />
                            <div>
                              <p className="font-medium">PayPal</p>
                              <p className="text-sm text-gray-600">Pay with your PayPal account</p>
                            </div>
                          </div>
                        </div>

                        <div
                          className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                            paymentMethod === 'bank_transfer' ? 'border-primary bg-primary/5' : 'border-gray-200'
                          }`}
                          onClick={() => setPaymentMethod('bank_transfer')}
                        >
                          <div className="flex items-center gap-3">
                            <Package className="h-5 w-5" />
                            <div>
                              <p className="font-medium">Bank Transfer</p>
                              <p className="text-sm text-gray-600">Direct bank transfer</p>
                            </div>
                          </div>
                        </div>

                        <div
                          className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                            paymentMethod === 'cash_on_delivery' ? 'border-primary bg-primary/5' : 'border-gray-200'
                          }`}
                          onClick={() => setPaymentMethod('cash_on_delivery')}
                        >
                          <div className="flex items-center gap-3">
                            <Truck className="h-5 w-5" />
                            <div>
                              <p className="font-medium">Cash on Delivery</p>
                              <p className="text-sm text-gray-600">Pay when you receive</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Step 4: Review Order */}
                {currentStep === 4 && (
                  <motion.div
                    initial="initial"
                    animate="animate"
                    variants={fadeInUp}
                    className="space-y-6"
                  >
                    <div className="flex items-center gap-2 mb-6">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      <h2 className="text-xl font-semibold">Review Your Order</h2>
                    </div>

                    <div className="space-y-6">
                      {/* Shipping Address Review */}
                      <div>
                        <h3 className="font-medium mb-2">Shipping Address</h3>
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <p>{shippingAddress.firstName} {shippingAddress.lastName}</p>
                          <p>{shippingAddress.street}</p>
                          <p>{shippingAddress.city}, {shippingAddress.state} {shippingAddress.zipCode}</p>
                          <p>{shippingAddress.country}</p>
                          {shippingAddress.phone && <p>Phone: {shippingAddress.phone}</p>}
                        </div>
                      </div>

                      {/* Billing Address Review */}
                      <div>
                        <h3 className="font-medium mb-2">Billing Address</h3>
                        <div className="p-4 bg-gray-50 rounded-lg">
                          {sameAsShipping ? (
                            <p className="text-gray-600">Same as shipping address</p>
                          ) : (
                            <>
                              <p>{billingAddress.firstName} {billingAddress.lastName}</p>
                              <p>{billingAddress.street}</p>
                              <p>{billingAddress.city}, {billingAddress.state} {billingAddress.zipCode}</p>
                              <p>{billingAddress.country}</p>
                            </>
                          )}
                        </div>
                      </div>

                      {/* Payment Method Review */}
                      <div>
                        <h3 className="font-medium mb-2">Payment Method</h3>
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <p className="capitalize">{paymentMethod.replace('_', ' ')}</p>
                        </div>
                      </div>

                      {/* Order Items Review */}
                      <div>
                        <h3 className="font-medium mb-2">Order Items</h3>
                        <div className="space-y-3">
                          {cartItems.map((item) => (
                            <div key={item.id} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                              <img
                                src={item.product.images[0]?.url || '/placeholder.jpg'}
                                alt={item.product.name}
                                className="w-16 h-16 object-cover rounded"
                              />
                              <div className="flex-1">
                                <p className="font-medium">{item.product.name}</p>
                                <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                                <p className="text-sm text-gray-600">Price: ${item.price}</p>
                              </div>
                              <p className="font-medium">${(item.price * item.quantity).toFixed(2)}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-8">
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    disabled={currentStep === 1}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>

                  {currentStep < 4 ? (
                    <Button
                      onClick={nextStep}
                      disabled={!validateStep(currentStep)}
                    >
                      Next
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  ) : (
                    <Button
                      onClick={placeOrder}
                      disabled={submitting}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {submitting ? 'Placing Order...' : 'Place Order'}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Cart Items */}
                <div className="space-y-3">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex items-center gap-3">
                      <img 
                        src={item.product.images[0]?.url || '/placeholder.jpg'} 
                        alt={item.product.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div className="flex-1">
                        <p className="font-medium text-sm">{item.product.name}</p>
                        <p className="text-xs text-gray-600">Qty: {item.quantity}</p>
                      </div>
                      <p className="font-medium">${(item.price * item.quantity).toFixed(2)}</p>
                    </div>
                  ))}
                </div>

                <Separator />

                {/* Totals */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax</span>
                    <span>${tax.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="flex items-center gap-1">
                      <Truck className="h-4 w-4" />
                      Shipping
                    </span>
                    <span>
                      {shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span>${total.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
