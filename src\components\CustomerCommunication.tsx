"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  MessageSquare, 
  Send, 
  Eye, 
  Filter,
  Search,
  AlertCircle,
  User,
  Mail,
  Calendar,
  Package,
  Flag,
  CheckCircle,
  Clock,
  MessageCircle
} from 'lucide-react';
import { ContactMessage, MessageReply } from '@/types';

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.3 }
};

const stagger = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high': return 'bg-red-500';
    case 'medium': return 'bg-yellow-500';
    case 'low': return 'bg-green-500';
    default: return 'bg-gray-500';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'unread': return 'bg-red-500';
    case 'read': return 'bg-blue-500';
    case 'replied': return 'bg-green-500';
    case 'closed': return 'bg-gray-500';
    default: return 'bg-gray-500';
  }
};

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'order': return <Package className="h-4 w-4" />;
    case 'support': return <MessageSquare className="h-4 w-4" />;
    case 'complaint': return <AlertCircle className="h-4 w-4" />;
    case 'feedback': return <MessageCircle className="h-4 w-4" />;
    default: return <Mail className="h-4 w-4" />;
  }
};

export default function CustomerCommunication() {
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedMessage, setSelectedMessage] = useState<ContactMessage | null>(null);
  const [replies, setReplies] = useState<MessageReply[]>([]);
  const [replyContent, setReplyContent] = useState('');
  const [submittingReply, setSubmittingReply] = useState(false);

  // Fetch messages
  useEffect(() => {
    fetchMessages();
  }, []);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/messages');
      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch message details with replies
  const fetchMessageDetails = async (messageId: string) => {
    try {
      const response = await fetch(`/api/messages/${messageId}`);
      if (response.ok) {
        const data = await response.json();
        setSelectedMessage(data.message);
        setReplies(data.replies || []);
        
        // Mark as read if unread
        if (data.message.status === 'unread') {
          await updateMessageStatus(messageId, 'read');
        }
      }
    } catch (error) {
      console.error('Error fetching message details:', error);
    }
  };

  // Update message status
  const updateMessageStatus = async (messageId: string, status: string) => {
    try {
      const response = await fetch(`/api/messages/${messageId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });
      
      if (response.ok) {
        const updatedMessage = await response.json();
        setMessages(messages.map(msg => 
          msg.id === messageId ? updatedMessage : msg
        ));
        if (selectedMessage?.id === messageId) {
          setSelectedMessage(updatedMessage);
        }
      }
    } catch (error) {
      console.error('Error updating message status:', error);
    }
  };

  // Send reply
  const sendReply = async () => {
    if (!selectedMessage || !replyContent.trim()) return;

    try {
      setSubmittingReply(true);
      const response = await fetch(`/api/messages/${selectedMessage.id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: replyContent,
          adminId: 'admin1' // This would be the logged-in admin's ID
        })
      });

      if (response.ok) {
        const newReply = await response.json();
        setReplies([...replies, newReply]);
        setReplyContent('');
        
        // Update message status to replied
        await updateMessageStatus(selectedMessage.id, 'replied');
      }
    } catch (error) {
      console.error('Error sending reply:', error);
    } finally {
      setSubmittingReply(false);
    }
  };

  // Filter messages
  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         message.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         message.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         message.message.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || message.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || message.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const MessageCard = ({ message }: { message: ContactMessage }) => (
    <motion.div variants={fadeInUp}>
      <Card className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => fetchMessageDetails(message.id)}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg flex items-center gap-2">
                {getCategoryIcon(message.category)}
                {message.subject}
              </CardTitle>
              <CardDescription className="flex items-center gap-4 mt-1">
                <span className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {message.name}
                </span>
                <span className="flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  {message.email}
                </span>
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {new Date(message.createdAt).toLocaleDateString()}
                </span>
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={`${getPriorityColor(message.priority)} text-white`}>
                {message.priority}
              </Badge>
              <Badge className={`${getStatusColor(message.status)} text-white`}>
                {message.status}
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <p className="text-sm text-gray-600 line-clamp-2 mb-3">
            {message.message}
          </p>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-xs text-gray-500">
              <span className="flex items-center gap-1">
                <Flag className="h-3 w-3" />
                {message.category}
              </span>
              {message.orderId && (
                <span className="flex items-center gap-1">
                  <Package className="h-3 w-3" />
                  Order #{message.orderId}
                </span>
              )}
            </div>
            
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  const MessageDetailsModal = ({ message }: { message: ContactMessage }) => (
    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center gap-2">
          {getCategoryIcon(message.category)}
          {message.subject}
        </DialogTitle>
      </DialogHeader>

      <div className="space-y-6">
        {/* Message Header */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-4">
            <div>
              <p className="font-medium">{message.name}</p>
              <p className="text-sm text-gray-600">{message.email}</p>
            </div>
            <div className="text-sm text-gray-500">
              <p>{new Date(message.createdAt).toLocaleString()}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={`${getPriorityColor(message.priority)} text-white`}>
              {message.priority}
            </Badge>
            <Badge className={`${getStatusColor(message.status)} text-white`}>
              {message.status}
            </Badge>
          </div>
        </div>

        {/* Original Message */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Original Message</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="whitespace-pre-wrap">{message.message}</p>
          </CardContent>
        </Card>

        {/* Replies */}
        {replies.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Replies</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {replies.map((reply) => (
                <div key={reply.id} className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <p className="font-medium text-sm">Admin Response</p>
                    <p className="text-xs text-gray-500">
                      {new Date(reply.createdAt).toLocaleString()}
                    </p>
                  </div>
                  <p className="text-sm whitespace-pre-wrap">{reply.content}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Reply Form */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Send Reply</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="reply">Reply Message</Label>
              <Textarea
                id="reply"
                placeholder="Type your reply..."
                value={replyContent}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setReplyContent(e.target.value)}
                rows={4}
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Button 
                onClick={sendReply}
                disabled={!replyContent.trim() || submittingReply}
              >
                <Send className="h-4 w-4 mr-2" />
                {submittingReply ? 'Sending...' : 'Send Reply'}
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Update Status
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => updateMessageStatus(message.id, 'read')}>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Mark as Read
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => updateMessageStatus(message.id, 'replied')}>
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Mark as Replied
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => updateMessageStatus(message.id, 'closed')}>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Close Message
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>
      </div>
    </DialogContent>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Customer Communication</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Array(6).fill(0).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Customer Communication</h2>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-red-50 text-red-600">
            {messages.filter(m => m.status === 'unread').length} unread
          </Badge>
          <Badge variant="outline" className="bg-blue-50 text-blue-600">
            {filteredMessages.length} total
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 max-w-sm">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Status: {statusFilter === 'all' ? 'All' : statusFilter}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setStatusFilter('all')}>All</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('unread')}>Unread</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('read')}>Read</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('replied')}>Replied</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('closed')}>Closed</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              <Flag className="h-4 w-4 mr-2" />
              Priority: {priorityFilter === 'all' ? 'All' : priorityFilter}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setPriorityFilter('all')}>All</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setPriorityFilter('high')}>High</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setPriorityFilter('medium')}>Medium</DropdownMenuItem>
            <DropdownMenuItem onClick={() => setPriorityFilter('low')}>Low</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Messages Grid */}
      <motion.div
        initial="initial"
        animate="animate"
        variants={stagger}
        className="grid grid-cols-1 md:grid-cols-2 gap-6"
      >
        {filteredMessages.map((message) => (
          <MessageCard key={message.id} message={message} />
        ))}
      </motion.div>

      {/* Empty State */}
      {filteredMessages.length === 0 && (
        <div className="text-center py-12">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
          <p className="text-gray-600">
            {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all'
              ? 'Try adjusting your search or filter criteria.'
              : 'No customer messages yet.'
            }
          </p>
        </div>
      )}

      {/* Message Details Modal */}
      <Dialog open={!!selectedMessage} onOpenChange={() => setSelectedMessage(null)}>
        {selectedMessage && <MessageDetailsModal message={selectedMessage} />}
      </Dialog>
    </div>
  );
}
