"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Package, 
  AlertTriangle, 
  TrendingDown, 
  TrendingUp,
  Search,
  Filter,
  Edit,
  Plus,
  Minus,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Product } from '@/types';

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.3 }
};

const stagger = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

interface InventoryStats {
  totalProducts: number;
  lowStockProducts: number;
  outOfStockProducts: number;
  totalValue: number;
}

export default function InventoryManagement() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [stockFilter, setStockFilter] = useState<'all' | 'low' | 'out'>('all');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isStockUpdateOpen, setIsStockUpdateOpen] = useState(false);
  const [newStockAmount, setNewStockAmount] = useState(0);
  const [updating, setUpdating] = useState(false);
  const [stats, setStats] = useState<InventoryStats>({
    totalProducts: 0,
    lowStockProducts: 0,
    outOfStockProducts: 0,
    totalValue: 0
  });

  // Fetch products
  useEffect(() => {
    fetchProducts();
  }, []);

  // Calculate stats when products change
  useEffect(() => {
    calculateStats();
  }, [products]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      if (response.ok) {
        const data = await response.json();
        setProducts(data);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = () => {
    const totalProducts = products.length;
    const lowStockProducts = products.filter(p => p.stock > 0 && p.stock <= 10).length;
    const outOfStockProducts = products.filter(p => p.stock === 0).length;
    const totalValue = products.reduce((sum, p) => sum + (p.price * p.stock), 0);

    setStats({
      totalProducts,
      lowStockProducts,
      outOfStockProducts,
      totalValue
    });
  };

  const updateStock = async () => {
    if (!selectedProduct) return;

    try {
      setUpdating(true);
      const response = await fetch(`/api/products/${selectedProduct.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ stock: newStockAmount })
      });

      if (response.ok) {
        const updatedProduct = await response.json();
        setProducts(products.map(p => 
          p.id === selectedProduct.id ? updatedProduct : p
        ));
        setIsStockUpdateOpen(false);
        setSelectedProduct(null);
        setNewStockAmount(0);
      }
    } catch (error) {
      console.error('Error updating stock:', error);
    } finally {
      setUpdating(false);
    }
  };

  const openStockUpdate = (product: Product) => {
    setSelectedProduct(product);
    setNewStockAmount(product.stock);
    setIsStockUpdateOpen(true);
  };

  // Filter products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesStock = true;
    if (stockFilter === 'low') {
      matchesStock = product.stock > 0 && product.stock <= 10;
    } else if (stockFilter === 'out') {
      matchesStock = product.stock === 0;
    }

    return matchesSearch && matchesStock;
  });

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { label: 'Out of Stock', color: 'bg-red-500', icon: <XCircle className="h-3 w-3" /> };
    if (stock <= 10) return { label: 'Low Stock', color: 'bg-yellow-500', icon: <AlertTriangle className="h-3 w-3" /> };
    return { label: 'In Stock', color: 'bg-green-500', icon: <CheckCircle className="h-3 w-3" /> };
  };

  const StatsCard = ({ title, value, icon, color }: { title: string; value: string | number; icon: React.ReactNode; color: string }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
          <div className={`p-3 rounded-full ${color}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const ProductCard = ({ product }: { product: Product }) => {
    const stockStatus = getStockStatus(product.stock);
    
    return (
      <motion.div variants={fadeInUp}>
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-lg">{product.name}</CardTitle>
                <p className="text-sm text-gray-600">{product.category}</p>
              </div>
              <Badge className={`${stockStatus.color} text-white flex items-center gap-1`}>
                {stockStatus.icon}
                {stockStatus.label}
              </Badge>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Product Image */}
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
              <img
                src={product.images[0]?.url || '/placeholder.jpg'}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Stock Info */}
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Current Stock:</span>
                <span className={`font-bold ${product.stock === 0 ? 'text-red-600' : product.stock <= 10 ? 'text-yellow-600' : 'text-green-600'}`}>
                  {product.stock} units
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Price:</span>
                <span className="font-bold">${product.price}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Stock Value:</span>
                <span className="font-bold">${(product.price * product.stock).toFixed(2)}</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="flex-1"
                onClick={() => openStockUpdate(product)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Update Stock
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  const StockUpdateModal = () => (
    <Dialog open={isStockUpdateOpen} onOpenChange={setIsStockUpdateOpen}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Update Stock - {selectedProduct?.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="currentStock">Current Stock</Label>
            <Input
              id="currentStock"
              value={selectedProduct?.stock || 0}
              disabled
              className="bg-gray-100"
            />
          </div>

          <div>
            <Label htmlFor="newStock">New Stock Amount</Label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setNewStockAmount(Math.max(0, newStockAmount - 1))}
                disabled={newStockAmount <= 0}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Input
                id="newStock"
                type="number"
                value={newStockAmount}
                onChange={(e) => setNewStockAmount(Math.max(0, parseInt(e.target.value) || 0))}
                className="text-center"
                min="0"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => setNewStockAmount(newStockAmount + 1)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button 
              variant="outline" 
              onClick={() => setIsStockUpdateOpen(false)}
              disabled={updating}
            >
              Cancel
            </Button>
            <Button 
              onClick={updateStock}
              disabled={updating}
            >
              {updating ? 'Updating...' : 'Update Stock'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Inventory Management</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array(4).fill(0).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Inventory Management</h2>
        <Badge variant="outline" className="bg-blue-50 text-blue-600">
          {filteredProducts.length} products
        </Badge>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Products"
          value={stats.totalProducts}
          icon={<Package className="h-6 w-6 text-white" />}
          color="bg-blue-500"
        />
        <StatsCard
          title="Low Stock Items"
          value={stats.lowStockProducts}
          icon={<AlertTriangle className="h-6 w-6 text-white" />}
          color="bg-yellow-500"
        />
        <StatsCard
          title="Out of Stock"
          value={stats.outOfStockProducts}
          icon={<XCircle className="h-6 w-6 text-white" />}
          color="bg-red-500"
        />
        <StatsCard
          title="Total Value"
          value={`$${stats.totalValue.toFixed(2)}`}
          icon={<TrendingUp className="h-6 w-6 text-white" />}
          color="bg-green-500"
        />
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 max-w-sm">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant={stockFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('all')}
          >
            All Stock
          </Button>
          <Button
            variant={stockFilter === 'low' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('low')}
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Low Stock
          </Button>
          <Button
            variant={stockFilter === 'out' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStockFilter('out')}
          >
            <XCircle className="h-4 w-4 mr-2" />
            Out of Stock
          </Button>
        </div>
      </div>

      {/* Products Grid */}
      <motion.div
        initial="initial"
        animate="animate"
        variants={stagger}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        {filteredProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </motion.div>

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-600">
            {searchTerm || stockFilter !== 'all' 
              ? 'Try adjusting your search or filter criteria.'
              : 'No products available.'
            }
          </p>
        </div>
      )}

      {/* Stock Update Modal */}
      <StockUpdateModal />
    </div>
  );
}
