"use client";

import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface RoleGuardProps {
  children: React.ReactNode;
  requiredRole: 'admin' | 'customer';
  fallbackPath?: string;
}

export default function RoleGuard({ children, requiredRole, fallbackPath }: RoleGuardProps) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // User is not authenticated, redirect to login
        router.push('/login');
        return;
      }

      if (user.role !== requiredRole) {
        // User doesn't have the required role
        if (fallbackPath) {
          router.push(fallbackPath);
        } else {
          // Redirect based on user role
          if (user.role === 'admin') {
            router.push('/admin');
          } else {
            router.push('/dashboard');
          }
        }
        return;
      }
    }
  }, [user, loading, requiredRole, fallbackPath, router]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show nothing if user is not authenticated or doesn't have required role
  if (!user || user.role !== requiredRole) {
    return null;
  }

  // User has the required role, render the children
  return <>{children}</>;
}
