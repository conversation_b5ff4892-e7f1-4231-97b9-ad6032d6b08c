"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

const loadingVariants = cva(
  "animate-spin",
  {
    variants: {
      size: {
        default: "h-4 w-4",
        sm: "h-3 w-3",
        lg: "h-6 w-6",
        xl: "h-8 w-8",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
)

interface LoadingProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingVariants> {
  text?: string
}

const Loading = React.forwardRef<HTMLDivElement, LoadingProps>(
  ({ className, size, text, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("flex items-center justify-center gap-2", className)}
        {...props}
      >
        <Loader2 className={cn(loadingVariants({ size }))} />
        {text && <span className="text-sm text-muted-foreground">{text}</span>}
      </div>
    )
  }
)
Loading.displayName = "Loading"

interface LoadingScreenProps {
  text?: string
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ text = "Loading..." }) => {
  return (
    <div className="flex min-h-screen items-center justify-center bg-background/95 backdrop-blur-sm">
      <div className="text-center space-y-4">
        <Loading size="xl" />
        <p className="text-lg text-muted-foreground">{text}</p>
      </div>
    </div>
  )
}

export { Loading, LoadingScreen, loadingVariants }
