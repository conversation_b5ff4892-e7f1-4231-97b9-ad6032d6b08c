import { db } from '@/firebase/config';
import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { Cart, CartItem, Product } from '@/types';

const CARTS_COLLECTION = 'carts';
const PRODUCTS_COLLECTION = 'products';

// Helper function to convert Firestore timestamp
const convertTimestamp = (timestamp: any): string => {
  if (timestamp?.toDate) {
    return timestamp.toDate().toISOString();
  }
  return timestamp || new Date().toISOString();
};

// Get user's cart
export const getUserCart = async (userId: string): Promise<Cart> => {
  try {
    const cartQuery = query(
      collection(db, CARTS_COLLECTION),
      where('userId', '==', userId),
      limit(1)
    );
    
    const cartSnapshot = await getDocs(cartQuery);
    
    if (cartSnapshot.empty) {
      // Create new cart if none exists
      const newCart: Omit<Cart, 'id'> = {
        userId,
        items: [],
        total: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      const docRef = await addDoc(collection(db, CARTS_COLLECTION), {
        ...newCart,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      
      return {
        id: docRef.id,
        ...newCart
      };
    }
    
    const cartDoc = cartSnapshot.docs[0];
    const cartData = cartDoc.data();
    
    return {
      id: cartDoc.id,
      userId: cartData.userId,
      items: cartData.items || [],
      total: cartData.total || 0,
      createdAt: convertTimestamp(cartData.createdAt),
      updatedAt: convertTimestamp(cartData.updatedAt)
    };
  } catch (error) {
    console.error('Error getting user cart:', error);
    throw new Error('Failed to get cart');
  }
};

// Add item to cart
export const addToCart = async (
  userId: string, 
  productId: string, 
  quantity: number = 1
): Promise<Cart> => {
  try {
    // Get product details
    const productDoc = await getDoc(doc(db, PRODUCTS_COLLECTION, productId));
    
    if (!productDoc.exists()) {
      throw new Error('Product not found');
    }
    
    const productData = productDoc.data();
    const product: Product = {
      id: productDoc.id,
      ...productData,
      createdAt: convertTimestamp(productData.createdAt),
      updatedAt: convertTimestamp(productData.updatedAt)
    } as Product;
    
    // Check stock availability
    if (product.stock < quantity) {
      throw new Error('Insufficient stock');
    }
    
    // Get user's cart
    const cart = await getUserCart(userId);
    
    // Check if item already exists in cart
    const existingItemIndex = cart.items.findIndex(item => item.productId === productId);
    
    if (existingItemIndex >= 0) {
      // Update existing item quantity
      const newQuantity = cart.items[existingItemIndex].quantity + quantity;
      
      if (newQuantity > product.stock) {
        throw new Error('Quantity exceeds available stock');
      }
      
      cart.items[existingItemIndex].quantity = newQuantity;
    } else {
      // Add new item to cart
      const newCartItem: CartItem = {
        id: `item_${Date.now()}_${productId}`,
        productId,
        product,
        quantity,
        price: product.price
      };
      
      cart.items.push(newCartItem);
    }
    
    // Recalculate total
    cart.total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cart.updatedAt = new Date().toISOString();
    
    // Update cart in database
    await updateDoc(doc(db, CARTS_COLLECTION, cart.id), {
      items: cart.items,
      total: cart.total,
      updatedAt: Timestamp.now()
    });
    
    return cart;
  } catch (error) {
    console.error('Error adding to cart:', error);
    throw error;
  }
};

// Update cart item quantity
export const updateCartItemQuantity = async (
  userId: string,
  productId: string,
  quantity: number
): Promise<Cart> => {
  try {
    if (quantity < 0) {
      throw new Error('Invalid quantity');
    }
    
    // Get user's cart
    const cart = await getUserCart(userId);
    
    // Find the item in cart
    const itemIndex = cart.items.findIndex(item => item.productId === productId);
    
    if (itemIndex === -1) {
      throw new Error('Item not found in cart');
    }
    
    if (quantity === 0) {
      // Remove item from cart
      cart.items.splice(itemIndex, 1);
    } else {
      // Check stock availability
      const productDoc = await getDoc(doc(db, PRODUCTS_COLLECTION, productId));
      const productData = productDoc.data();
      
      if (productData && quantity > productData.stock) {
        throw new Error('Quantity exceeds available stock');
      }
      
      // Update quantity
      cart.items[itemIndex].quantity = quantity;
    }
    
    // Recalculate total
    cart.total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    cart.updatedAt = new Date().toISOString();
    
    // Update cart in database
    await updateDoc(doc(db, CARTS_COLLECTION, cart.id), {
      items: cart.items,
      total: cart.total,
      updatedAt: Timestamp.now()
    });
    
    return cart;
  } catch (error) {
    console.error('Error updating cart item quantity:', error);
    throw error;
  }
};

// Remove item from cart
export const removeFromCart = async (
  userId: string,
  productId: string
): Promise<Cart> => {
  try {
    return await updateCartItemQuantity(userId, productId, 0);
  } catch (error) {
    console.error('Error removing from cart:', error);
    throw error;
  }
};

// Clear entire cart
export const clearCart = async (userId: string): Promise<Cart> => {
  try {
    // Get user's cart
    const cart = await getUserCart(userId);
    
    // Clear all items
    cart.items = [];
    cart.total = 0;
    cart.updatedAt = new Date().toISOString();
    
    // Update cart in database
    await updateDoc(doc(db, CARTS_COLLECTION, cart.id), {
      items: [],
      total: 0,
      updatedAt: Timestamp.now()
    });
    
    return cart;
  } catch (error) {
    console.error('Error clearing cart:', error);
    throw error;
  }
};

// Get cart item count
export const getCartItemCount = async (userId: string): Promise<number> => {
  try {
    const cart = await getUserCart(userId);
    return cart.items.reduce((count, item) => count + item.quantity, 0);
  } catch (error) {
    console.error('Error getting cart item count:', error);
    return 0;
  }
};

// Validate cart items (check stock, prices, etc.)
export const validateCartItems = async (userId: string): Promise<{
  isValid: boolean;
  errors: string[];
  updatedCart?: Cart;
}> => {
  try {
    const cart = await getUserCart(userId);
    const errors: string[] = [];
    let hasChanges = false;
    
    // Check each item
    for (let i = cart.items.length - 1; i >= 0; i--) {
      const item = cart.items[i];
      
      // Get current product data
      const productDoc = await getDoc(doc(db, PRODUCTS_COLLECTION, item.productId));
      
      if (!productDoc.exists()) {
        // Product no longer exists
        cart.items.splice(i, 1);
        errors.push(`Product "${item.product.name}" is no longer available`);
        hasChanges = true;
        continue;
      }
      
      const currentProduct = productDoc.data();
      
      // Check if product is still active
      if (!currentProduct.isActive) {
        cart.items.splice(i, 1);
        errors.push(`Product "${item.product.name}" is no longer available`);
        hasChanges = true;
        continue;
      }
      
      // Check stock availability
      if (item.quantity > currentProduct.stock) {
        if (currentProduct.stock === 0) {
          cart.items.splice(i, 1);
          errors.push(`Product "${item.product.name}" is out of stock`);
        } else {
          cart.items[i].quantity = currentProduct.stock;
          errors.push(`Quantity for "${item.product.name}" reduced to available stock (${currentProduct.stock})`);
        }
        hasChanges = true;
      }
      
      // Check if price has changed
      if (item.price !== currentProduct.price) {
        cart.items[i].price = currentProduct.price;
        errors.push(`Price for "${item.product.name}" has been updated`);
        hasChanges = true;
      }
    }
    
    if (hasChanges) {
      // Recalculate total
      cart.total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      cart.updatedAt = new Date().toISOString();
      
      // Update cart in database
      await updateDoc(doc(db, CARTS_COLLECTION, cart.id), {
        items: cart.items,
        total: cart.total,
        updatedAt: Timestamp.now()
      });
      
      return {
        isValid: errors.length === 0,
        errors,
        updatedCart: cart
      };
    }
    
    return {
      isValid: true,
      errors: []
    };
  } catch (error) {
    console.error('Error validating cart items:', error);
    return {
      isValid: false,
      errors: ['Failed to validate cart items']
    };
  }
};
