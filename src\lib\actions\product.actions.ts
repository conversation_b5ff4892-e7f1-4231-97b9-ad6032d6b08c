import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  DocumentSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import { Product, CreateProductParams, UpdateProductParams, ProductImage } from '@/types';
import { uploadToCloudinary, deleteFromCloudinary } from '@/lib/cloudinary';

const PRODUCTS_COLLECTION = 'products';

// Helper function to convert Firestore timestamp to string
const convertTimestamp = (timestamp: any): string => {
  if (timestamp && typeof timestamp.toDate === 'function') {
    return timestamp.toDate().toISOString();
  }
  return new Date().toISOString();
};

// Create a new product
export const createProduct = async (
  productData: CreateProductParams,
  imageFiles: File[]
): Promise<Product> => {
  try {
    // Upload images to Cloudinary
    const uploadPromises = imageFiles.map(async (file, index) => {
      const buffer = Buffer.from(await file.arrayBuffer());
      const uploadResult = await uploadToCloudinary(buffer, {
        folder: 'ecommerce-products',
        public_id: `${productData.name.replace(/\s+/g, '-').toLowerCase()}-${index}`,
      });

      return {
        id: `img_${Date.now()}_${index}`,
        url: uploadResult.secure_url,
        public_id: uploadResult.public_id,
        alt: `${productData.name} image ${index + 1}`,
        isPrimary: index === 0,
      };
    });

    const images = await Promise.all(uploadPromises);

    // Create product document
    const productDoc = {
      ...productData,
      images,
      rating: 0,
      reviewCount: 0,
      isActive: productData.isActive ?? true,
      featured: productData.featured ?? false,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };

    const docRef = await addDoc(collection(db, PRODUCTS_COLLECTION), productDoc);
    
    // Return the created product
    const createdProduct: Product = {
      id: docRef.id,
      ...productDoc,
      createdAt: convertTimestamp(productDoc.createdAt),
      updatedAt: convertTimestamp(productDoc.updatedAt),
    };

    return createdProduct;
  } catch (error) {
    console.error('Error creating product:', error);
    throw new Error('Failed to create product');
  }
};

// Get all products with optional filtering
export const getProducts = async (options?: {
  category?: string;
  featured?: boolean;
  isActive?: boolean;
  limit?: number;
  lastDoc?: DocumentSnapshot;
}): Promise<{ products: Product[]; lastDoc?: DocumentSnapshot }> => {
  try {
    let q = query(collection(db, PRODUCTS_COLLECTION));

    // Apply filters
    if (options?.category) {
      q = query(q, where('category', '==', options.category));
    }
    if (options?.featured !== undefined) {
      q = query(q, where('featured', '==', options.featured));
    }
    if (options?.isActive !== undefined) {
      q = query(q, where('isActive', '==', options.isActive));
    }

    // Apply ordering and pagination
    q = query(q, orderBy('createdAt', 'desc'));
    
    if (options?.limit) {
      q = query(q, limit(options.limit));
    }
    
    if (options?.lastDoc) {
      q = query(q, startAfter(options.lastDoc));
    }

    const querySnapshot = await getDocs(q);
    const products: Product[] = [];
    let lastDoc: DocumentSnapshot | undefined;

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      products.push({
        id: doc.id,
        ...data,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
      } as Product);
    });

    if (querySnapshot.docs.length > 0) {
      lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
    }

    return { products, lastDoc };
  } catch (error) {
    console.error('Error getting products:', error);
    throw new Error('Failed to get products');
  }
};

// Get a single product by ID
export const getProductById = async (id: string): Promise<Product | null> => {
  try {
    const docRef = doc(db, PRODUCTS_COLLECTION, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        ...data,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
      } as Product;
    }

    return null;
  } catch (error) {
    console.error('Error getting product by ID:', error);
    throw new Error('Failed to get product');
  }
};

// Update a product
export const updateProduct = async (
  productData: UpdateProductParams,
  newImageFiles?: File[]
): Promise<Product> => {
  try {
    const { id, ...updateData } = productData;
    const docRef = doc(db, PRODUCTS_COLLECTION, id);

    // Get current product data
    const currentProductDoc = await getDoc(docRef);
    if (!currentProductDoc.exists()) {
      throw new Error('Product not found');
    }

    const currentProduct = currentProductDoc.data() as Product;
    let updatedImages = currentProduct.images;

    // Handle new image uploads
    if (newImageFiles && newImageFiles.length > 0) {
      const uploadPromises = newImageFiles.map(async (file, index) => {
        const buffer = Buffer.from(await file.arrayBuffer());
        const uploadResult = await uploadToCloudinary(buffer, {
          folder: 'ecommerce-products',
          public_id: `${updateData.name || currentProduct.name}-${Date.now()}-${index}`,
        });

        return {
          id: `img_${Date.now()}_${index}`,
          url: uploadResult.secure_url,
          public_id: uploadResult.public_id,
          alt: `${updateData.name || currentProduct.name} image ${index + 1}`,
          isPrimary: index === 0 && currentProduct.images.length === 0,
        };
      });

      const newImages = await Promise.all(uploadPromises);
      updatedImages = [...currentProduct.images, ...newImages];
    }

    // Update product document
    const updateDocData = {
      ...updateData,
      images: updatedImages,
      updatedAt: Timestamp.now(),
    };

    await updateDoc(docRef, updateDocData);

    // Return updated product
    const updatedProduct: Product = {
      ...currentProduct,
      ...updateData,
      id,
      images: updatedImages,
      updatedAt: convertTimestamp(updateDocData.updatedAt),
    };

    return updatedProduct;
  } catch (error) {
    console.error('Error updating product:', error);
    throw new Error('Failed to update product');
  }
};

// Delete a product
export const deleteProduct = async (id: string): Promise<void> => {
  try {
    const docRef = doc(db, PRODUCTS_COLLECTION, id);
    
    // Get product data to delete images from Cloudinary
    const productDoc = await getDoc(docRef);
    if (productDoc.exists()) {
      const product = productDoc.data() as Product;
      
      // Delete images from Cloudinary
      const deletePromises = product.images.map(async (image) => {
        try {
          await deleteFromCloudinary(image.public_id);
        } catch (error) {
          console.error(`Failed to delete image ${image.public_id}:`, error);
        }
      });

      await Promise.all(deletePromises);
    }

    // Delete the product document
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting product:', error);
    throw new Error('Failed to delete product');
  }
};

// Delete a specific image from a product
export const deleteProductImage = async (productId: string, imageId: string): Promise<void> => {
  try {
    const docRef = doc(db, PRODUCTS_COLLECTION, productId);
    const productDoc = await getDoc(docRef);
    
    if (!productDoc.exists()) {
      throw new Error('Product not found');
    }

    const product = productDoc.data() as Product;
    const imageToDelete = product.images.find(img => img.id === imageId);
    
    if (!imageToDelete) {
      throw new Error('Image not found');
    }

    // Delete image from Cloudinary
    await deleteFromCloudinary(imageToDelete.public_id);

    // Remove image from product
    const updatedImages = product.images.filter(img => img.id !== imageId);
    
    // If we deleted the primary image, make the first remaining image primary
    if (imageToDelete.isPrimary && updatedImages.length > 0) {
      updatedImages[0].isPrimary = true;
    }

    await updateDoc(docRef, {
      images: updatedImages,
      updatedAt: Timestamp.now(),
    });
  } catch (error) {
    console.error('Error deleting product image:', error);
    throw new Error('Failed to delete product image');
  }
};

// Search products
export const searchProducts = async (searchTerm: string, options?: {
  category?: string;
  limitCount?: number;
}): Promise<Product[]> => {
  try {
    let q = query(collection(db, PRODUCTS_COLLECTION));

    // Apply filters
    if (options?.category) {
      q = query(q, where('category', '==', options.category));
    }

    q = query(q, where('isActive', '==', true));
    q = query(q, orderBy('createdAt', 'desc'));

    if (options?.limitCount) {
      q = query(q, limit(options.limitCount));
    }

    const querySnapshot = await getDocs(q);
    const products: Product[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const product = {
        id: doc.id,
        ...data,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
      } as Product;

      // Filter by search term (case-insensitive)
      const searchTermLower = searchTerm.toLowerCase();
      if (
        product.name.toLowerCase().includes(searchTermLower) ||
        product.description.toLowerCase().includes(searchTermLower) ||
        product.category.toLowerCase().includes(searchTermLower) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTermLower))
      ) {
        products.push(product);
      }
    });

    return products;
  } catch (error) {
    console.error('Error searching products:', error);
    throw new Error('Failed to search products');
  }
};

// Get products by category
export const getProductsByCategory = async (category: string, limitCount?: number): Promise<Product[]> => {
  try {
    let q = query(
      collection(db, PRODUCTS_COLLECTION),
      where('category', '==', category),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );

    if (limitCount) {
      q = query(q, limit(limitCount));
    }

    const querySnapshot = await getDocs(q);
    const products: Product[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      products.push({
        id: doc.id,
        ...data,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
      } as Product);
    });

    return products;
  } catch (error) {
    console.error('Error getting products by category:', error);
    throw new Error('Failed to get products by category');
  }
};

// Get featured products
export const getFeaturedProducts = async (limitCount?: number): Promise<Product[]> => {
  try {
    let q = query(
      collection(db, PRODUCTS_COLLECTION),
      where('featured', '==', true),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );

    if (limitCount) {
      q = query(q, limit(limitCount));
    }

    const querySnapshot = await getDocs(q);
    const products: Product[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      products.push({
        id: doc.id,
        ...data,
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt),
      } as Product);
    });

    return products;
  } catch (error) {
    console.error('Error getting featured products:', error);
    throw new Error('Failed to get featured products');
  }
};
