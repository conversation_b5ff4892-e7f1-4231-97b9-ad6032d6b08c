import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'dkfrj168i',
  api_key: process.env.CLOUDINARY_API_KEY || '758867318889231',
  api_secret: process.env.CLOUDINARY_API_SECRET || '1jXp9XsW-GFE74ryxQMsQbh1Jl0',
});

export interface CloudinaryUploadResult {
  public_id: string;
  secure_url: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
  created_at: string;
  bytes: number;
}

export const uploadToCloudinary = async (
  file: Buffer | string,
  options?: {
    folder?: string;
    public_id?: string;
    transformation?: any;
  }
): Promise<CloudinaryUploadResult> => {
  try {
    const uploadOptions = {
      folder: options?.folder || 'ecommerce-products',
      public_id: options?.public_id,
      transformation: options?.transformation,
      resource_type: 'auto' as const,
    };

    // Convert Buffer to base64 string if needed
    const fileToUpload = Buffer.isBuffer(file) 
      ? `data:image/jpeg;base64,${file.toString('base64')}`
      : file;

    const result = await cloudinary.uploader.upload(fileToUpload, uploadOptions);
    
    return {
      public_id: result.public_id,
      secure_url: result.secure_url,
      width: result.width,
      height: result.height,
      format: result.format,
      resource_type: result.resource_type,
      created_at: result.created_at,
      bytes: result.bytes,
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload image to Cloudinary');
  }
};

export const deleteFromCloudinary = async (public_id: string): Promise<void> => {
  try {
    await cloudinary.uploader.destroy(public_id);
  } catch (error) {
    console.error('Cloudinary delete error:', error);
    throw new Error('Failed to delete image from Cloudinary');
  }
};

export const getCloudinaryUrl = (
  public_id: string,
  transformations?: {
    width?: number;
    height?: number;
    crop?: string;
    quality?: string;
    format?: string;
  }
): string => {
  if (!transformations) {
    return cloudinary.url(public_id);
  }

  return cloudinary.url(public_id, {
    width: transformations.width,
    height: transformations.height,
    crop: transformations.crop,
    quality: transformations.quality,
    format: transformations.format,
  });
};

export default cloudinary;
