// Database seeding script to populate Firestore with sample data
import { db } from '@/firebase/admin';
import { Product, Order, ContactMessage } from '@/types';

// Sample products data
const sampleProducts: Omit<Product, 'id'>[] = [
  {
    name: "Premium Wireless Headphones",
    description: "High-quality wireless headphones with noise cancellation, premium sound quality, and 30-hour battery life. Perfect for music lovers and professionals.",
    price: 299.99,
    originalPrice: 399.99,
    images: [
      {
        id: "1",
        url: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=600&fit=crop",
        public_id: "headphones-1",
        alt: "Premium Wireless Headphones",
        isPrimary: true
      }
    ],
    category: "Electronics",
    subcategory: "Audio",
    brand: "AudioTech",
    stock: 25,
    featured: true,
    isActive: true,
    tags: ["wireless", "headphones", "noise-cancelling", "premium", "audio"],
    specifications: {
      "Battery Life": "30 hours",
      "Driver Size": "40mm",
      "Frequency Response": "20Hz - 20kHz",
      "Bluetooth Version": "5.0",
      "Weight": "250g"
    },
    rating: 4.8,
    reviewCount: 1234,
    weight: 0.25,
    dimensions: {
      length: 20,
      width: 18,
      height: 8
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    name: "Smart Fitness Watch",
    description: "Advanced fitness tracker with heart rate monitoring, GPS, sleep tracking, and 7-day battery life. Perfect for fitness enthusiasts.",
    price: 199.99,
    originalPrice: 249.99,
    images: [
      {
        id: "2",
        url: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=600&fit=crop",
        public_id: "watch-1",
        alt: "Smart Fitness Watch",
        isPrimary: true
      }
    ],
    category: "Electronics",
    subcategory: "Wearables",
    brand: "FitTech",
    stock: 15,
    featured: true,
    isActive: true,
    tags: ["smartwatch", "fitness", "gps", "heart-rate", "waterproof"],
    specifications: {
      "Display": "1.4\" AMOLED",
      "Battery Life": "7 days",
      "Water Resistance": "5ATM",
      "GPS": "Built-in",
      "Heart Rate": "Optical sensor"
    },
    rating: 4.9,
    reviewCount: 856,
    weight: 0.045,
    dimensions: {
      length: 4.5,
      width: 4.5,
      height: 1.2
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    name: "Organic Cotton T-Shirt",
    description: "Soft, comfortable organic cotton t-shirt made from sustainable materials. Available in multiple colors and sizes.",
    price: 29.99,
    originalPrice: 39.99,
    images: [
      {
        id: "3",
        url: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=600&h=600&fit=crop",
        public_id: "tshirt-1",
        alt: "Organic Cotton T-Shirt",
        isPrimary: true
      }
    ],
    category: "Clothing",
    subcategory: "T-Shirts",
    brand: "EcoWear",
    stock: 50,
    featured: false,
    isActive: true,
    tags: ["organic", "cotton", "sustainable", "comfortable", "casual"],
    specifications: {
      "Material": "100% Organic Cotton",
      "Fit": "Regular",
      "Care": "Machine wash cold",
      "Origin": "Responsibly sourced"
    },
    rating: 4.7,
    reviewCount: 2341,
    weight: 0.15,
    dimensions: {
      length: 30,
      width: 25,
      height: 1
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Sample messages data
const sampleMessages: Omit<ContactMessage, 'id'>[] = [
  {
    userId: 'user1',
    name: 'John Doe',
    email: '<EMAIL>',
    subject: 'Order Delivery Issue',
    message: 'I have not received my order yet. It was supposed to arrive yesterday.',
    status: 'unread',
    priority: 'high',
    category: 'order',
    orderId: '1',
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
  },
  {
    name: 'Jane Smith',
    email: '<EMAIL>',
    subject: 'Product Inquiry',
    message: 'Can you tell me more about the wireless headphones? Are they compatible with all devices?',
    status: 'read',
    priority: 'medium',
    category: 'general',
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString()
  },
  {
    userId: 'user3',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    subject: 'Complaint about Service',
    message: 'The customer service was very poor. I waited for 2 hours to get a response.',
    status: 'replied',
    priority: 'high',
    category: 'complaint',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
  }
];

// Function to seed the database
export async function seedDatabase() {
  try {
    console.log('Starting database seeding...');
    
    // Clear existing data
    console.log('Clearing existing data...');
    
    // Clear products
    const productsSnapshot = await db.collection('products').get();
    const productDeletePromises = productsSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(productDeletePromises);
    
    // Clear messages
    const messagesSnapshot = await db.collection('messages').get();
    const messageDeletePromises = messagesSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(messageDeletePromises);
    
    // Clear orders
    const ordersSnapshot = await db.collection('orders').get();
    const orderDeletePromises = ordersSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(orderDeletePromises);
    
    // Add sample products
    console.log('Adding sample products...');
    const productPromises = sampleProducts.map(product => 
      db.collection('products').add(product)
    );
    await Promise.all(productPromises);
    
    // Add sample messages
    console.log('Adding sample messages...');
    const messagePromises = sampleMessages.map(message => 
      db.collection('messages').add(message)
    );
    await Promise.all(messagePromises);
    
    console.log('Database seeding completed successfully!');
    
    return {
      success: true,
      message: 'Database seeded successfully',
      data: {
        products: sampleProducts.length,
        messages: sampleMessages.length
      }
    };
  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}
