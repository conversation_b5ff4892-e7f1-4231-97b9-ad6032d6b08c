import { NextRequest, NextResponse } from 'next/server';

// Define the admin email
const ADMIN_EMAIL = '<EMAIL>';

// Pages that admins are NOT allowed to access
const ADMIN_RESTRICTED_PAGES = [
  '/dashboard',
  '/products',
  '/cart',
  '/contact'
];

// Pages that customers are NOT allowed to access
const CUSTOMER_RESTRICTED_PAGES = [
  '/admin'
];

// Public pages that everyone can access
const PUBLIC_PAGES = [
  '/',
  '/login',
  '/signup'
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Allow access to API routes
  if (pathname.startsWith('/api/')) {
    return NextResponse.next();
  }
  
  // If user is admin and tries to access /login or /, redirect to /admin
  const sessionCookie = request.cookies.get('session');
  const userRole = request.cookies.get('userRole')?.value;
  if (sessionCookie && userRole === 'admin' && (pathname === '/' || pathname === '/login')) {
    return NextResponse.redirect(new URL('/admin', request.url));
  }

  // Allow access to public pages
  if (PUBLIC_PAGES.includes(pathname)) {
    return NextResponse.next();
  }
  
  // Check if user has a session
  if (!sessionCookie) {
    // Redirect to login if no session
    return NextResponse.redirect(new URL('/login', request.url));
  }

  if (!userRole) {
    // If no role is set, redirect to login
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  // Check role-based restrictions
  if (userRole === 'admin') {
    // Admin users can only access admin dashboard and public pages
    if (ADMIN_RESTRICTED_PAGES.some(page => pathname.startsWith(page))) {
      return NextResponse.redirect(new URL('/admin', request.url));
    }
  } else if (userRole === 'customer') {
    // Customer users cannot access admin pages
    if (CUSTOMER_RESTRICTED_PAGES.some(page => pathname.startsWith(page))) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|api).*)',
  ]
};
