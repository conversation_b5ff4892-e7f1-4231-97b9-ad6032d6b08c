// User types
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'customer';
  createdAt: string;
  updatedAt: string;
}

// Auth types
export interface SignUpParams {
  name: string;
  email: string;
  password: string;
}

export interface SignInParams {
  email: string;
  password: string;
}

// Product types
export interface ProductImage {
  id: string;
  url: string;
  public_id: string;
  alt: string;
  isPrimary: boolean;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: ProductImage[];
  category: string;
  subcategory?: string;
  brand?: string;
  stock: number;
  featured: boolean;
  isActive: boolean;
  tags: string[];
  specifications?: Record<string, any>;
  rating: number;
  reviewCount: number;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductParams {
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  subcategory?: string;
  brand?: string;
  stock: number;
  featured?: boolean;
  isActive?: boolean;
  tags: string[];
  specifications?: Record<string, any>;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

export interface UpdateProductParams {
  id: string;
  name?: string;
  description?: string;
  price?: number;
  originalPrice?: number;
  category?: string;
  subcategory?: string;
  brand?: string;
  stock?: number;
  featured?: boolean;
  isActive?: boolean;
  tags?: string[];
  specifications?: Record<string, any>;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

// Cart types
export interface CartItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  price: number;
}

export interface Cart {
  id: string;
  userId: string;
  items: CartItem[];
  total: number;
  createdAt: string;
  updatedAt: string;
}

// Order types
export interface OrderItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  price: number;
  subtotal: number;
}

export interface Order {
  id: string;
  userId: string;
  user: User;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: 'credit_card' | 'paypal' | 'bank_transfer' | 'cash_on_delivery';
  shippingAddress: Address;
  billingAddress: Address;
  notes?: string;
  trackingNumber?: string;
  estimatedDelivery?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  firstName: string;
  lastName: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

// Customer Communication types
export interface ContactMessage {
  id: string;
  userId?: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'unread' | 'read' | 'replied' | 'closed';
  priority: 'low' | 'medium' | 'high';
  category: 'general' | 'support' | 'complaint' | 'feedback' | 'order';
  orderId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MessageReply {
  id: string;
  messageId: string;
  adminId: string;
  content: string;
  createdAt: string;
}

// Dashboard Analytics types
export interface DashboardStats {
  totalProducts: number;
  totalUsers: number;
  totalOrders: number;
  totalRevenue: number;
  pendingOrders: number;
  unreadMessages: number;
  lowStockProducts: number;
  topProducts: Product[];
  recentOrders: Order[];
  monthlyRevenue: number[];
  monthlyOrders: number[];
}

// Customer Dashboard types
export interface CustomerDashboardData {
  user: User;
  recentOrders: Order[];
  wishlist: Product[];
  cart: Cart;
  addresses: Address[];
  messages: ContactMessage[];
}
