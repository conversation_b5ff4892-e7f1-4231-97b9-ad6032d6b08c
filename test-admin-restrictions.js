// Test script to verify admin access restrictions
// This script tests that admin users can only access the admin dashboard

const BASE_URL = 'http://localhost:3000';

// Test credentials
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

const customerCredentials = {
  email: '<EMAIL>',
  password: 'customer123'
};

// Helper function to make HTTP requests
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
    
    return {
      status: response.status,
      headers: response.headers,
      data: response.status === 200 ? await response.json() : null,
      redirected: response.redirected,
      url: response.url
    };
  } catch (error) {
    console.error('Request failed:', error);
    return null;
  }
}

// Test admin access restrictions
async function testAdminAccessRestrictions() {
  console.log('🔧 Testing Admin Access Restrictions...\n');
  
  // First, create a customer account for testing
  console.log('1. Creating customer account...');
  const customerSignup = await makeRequest(`${BASE_URL}/api/auth/signup`, {
    method: 'POST',
    body: JSON.stringify(customerCredentials)
  });
  
  if (customerSignup && customerSignup.status === 201) {
    console.log('✅ Customer account created successfully');
  } else {
    console.log('ℹ️  Customer account might already exist');
  }
  
  // Test admin login
  console.log('\n2. Testing admin login...');
  const adminLogin = await makeRequest(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    body: JSON.stringify(adminCredentials)
  });
  
  if (adminLogin && adminLogin.status === 200) {
    console.log('✅ Admin login successful');
    
    // Get admin session cookie
    const adminSessionCookie = adminLogin.headers.get('set-cookie');
    
    // Test admin access to restricted pages
    console.log('\n3. Testing admin access to restricted pages...');
    
    const restrictedPages = [
      '/dashboard',
      '/products',
      '/cart',
      '/contact'
    ];
    
    for (const page of restrictedPages) {
      console.log(`\nTesting admin access to ${page}...`);
      
      // Make request with admin session
      const pageResponse = await makeRequest(`${BASE_URL}${page}`, {
        method: 'GET',
        headers: {
          'Cookie': adminSessionCookie
        },
        redirect: 'manual'
      });
      
      if (pageResponse) {
        if (pageResponse.status === 302 || pageResponse.redirected) {
          console.log(`✅ Admin correctly redirected from ${page}`);
          console.log(`   Redirect URL: ${pageResponse.url}`);
        } else if (pageResponse.status === 200) {
          console.log(`❌ Admin was able to access ${page} (should be restricted)`);
        } else {
          console.log(`⚠️  Unexpected response for ${page}: ${pageResponse.status}`);
        }
      }
    }
    
    // Test admin access to allowed pages
    console.log('\n4. Testing admin access to allowed pages...');
    const allowedPages = ['/admin'];
    
    for (const page of allowedPages) {
      console.log(`\nTesting admin access to ${page}...`);
      
      const pageResponse = await makeRequest(`${BASE_URL}${page}`, {
        method: 'GET',
        headers: {
          'Cookie': adminSessionCookie
        }
      });
      
      if (pageResponse && pageResponse.status === 200) {
        console.log(`✅ Admin can access ${page} (correct)`);
      } else {
        console.log(`❌ Admin cannot access ${page} (should be allowed)`);
      }
    }
  } else {
    console.log('❌ Admin login failed');
  }
  
  // Test customer access
  console.log('\n5. Testing customer access...');
  const customerLogin = await makeRequest(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    body: JSON.stringify(customerCredentials)
  });
  
  if (customerLogin && customerLogin.status === 200) {
    console.log('✅ Customer login successful');
    
    const customerSessionCookie = customerLogin.headers.get('set-cookie');
    
    // Test customer access to customer pages
    const customerPages = ['/dashboard', '/products', '/cart', '/contact'];
    
    for (const page of customerPages) {
      console.log(`\nTesting customer access to ${page}...`);
      
      const pageResponse = await makeRequest(`${BASE_URL}${page}`, {
        method: 'GET',
        headers: {
          'Cookie': customerSessionCookie
        }
      });
      
      if (pageResponse && pageResponse.status === 200) {
        console.log(`✅ Customer can access ${page} (correct)`);
      } else {
        console.log(`❌ Customer cannot access ${page} (should be allowed)`);
      }
    }
    
    // Test customer access to admin pages
    console.log('\nTesting customer access to admin pages...');
    const adminPageResponse = await makeRequest(`${BASE_URL}/admin`, {
      method: 'GET',
      headers: {
        'Cookie': customerSessionCookie
      },
      redirect: 'manual'
    });
    
    if (adminPageResponse && (adminPageResponse.status === 302 || adminPageResponse.redirected)) {
      console.log('✅ Customer correctly redirected from admin page');
    } else {
      console.log('❌ Customer was able to access admin page (should be restricted)');
    }
  } else {
    console.log('❌ Customer login failed');
  }
  
  console.log('\n🎉 Admin access restriction testing completed!');
}

// Run the test
testAdminAccessRestrictions().catch(console.error);
