#!/usr/bin/env node

// Test script to verify CRUD operations and role-based redirections
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

// Test data
const testProduct = {
  name: 'Test Product',
  description: 'Test Description',
  price: 99.99,
  category: 'Electronics',
  brand: 'TestBrand',
  stock: 10,
  featured: true,
  isActive: true,
  tags: ['test', 'product']
};

const testUser = {
  email: '<EMAIL>',
  password: 'testpass123',
  name: 'Test User'
};

const adminUser = {
  email: '<EMAIL>',
  password: 'adminpass123',
  name: 'Admin User'
};

async function testAPI() {
  console.log('🚀 Starting E-Commerce API Tests...\n');

  // Test 1: Check if server is running
  try {
    const response = await fetch(`${BASE_URL}/api/products`);
    console.log('✅ Server is running');
    console.log(`📊 Products API Status: ${response.status}\n`);
  } catch (error) {
    console.log('❌ Server is not running');
    return;
  }

  // Test 2: Test product CRUD operations
  console.log('🔍 Testing Product CRUD Operations...');
  
  try {
    // GET all products
    const getResponse = await fetch(`${BASE_URL}/api/products`);
    const products = await getResponse.json();
    console.log(`✅ GET Products: ${products.length} products found`);

    // POST new product (using JSON)
    const postResponse = await fetch(`${BASE_URL}/api/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testProduct)
    });
    
    if (postResponse.ok) {
      const newProduct = await postResponse.json();
      console.log(`✅ POST Product: Created product with ID ${newProduct.id}`);
      
      // PUT update product
      const updateFormData = new FormData();
      updateFormData.append('name', 'Updated Test Product');
      updateFormData.append('price', '199.99');
      
      const putResponse = await fetch(`${BASE_URL}/api/products/${newProduct.id}`, {
        method: 'PUT',
        body: updateFormData
      });
      
      if (putResponse.ok) {
        console.log(`✅ PUT Product: Updated product ${newProduct.id}`);
      } else {
        console.log(`❌ PUT Product: Failed to update product`);
      }
      
      // DELETE product
      const deleteResponse = await fetch(`${BASE_URL}/api/products/${newProduct.id}`, {
        method: 'DELETE'
      });
      
      if (deleteResponse.ok) {
        console.log(`✅ DELETE Product: Deleted product ${newProduct.id}`);
      } else {
        console.log(`❌ DELETE Product: Failed to delete product`);
      }
    } else {
      console.log(`❌ POST Product: Failed to create product`);
    }
  } catch (error) {
    console.log(`❌ Product CRUD Error: ${error.message}`);
  }

  // Test 3: Test order management
  console.log('\n🔍 Testing Order Management...');
  
  try {
    const orderResponse = await fetch(`${BASE_URL}/api/orders`);
    const orders = await orderResponse.json();
    console.log(`✅ GET Orders: ${orders.length} orders found`);
  } catch (error) {
    console.log(`❌ Order Management Error: ${error.message}`);
  }

  // Test 4: Test message system
  console.log('\n🔍 Testing Message System...');
  
  try {
    const messageResponse = await fetch(`${BASE_URL}/api/messages`);
    const messages = await messageResponse.json();
    console.log(`✅ GET Messages: ${messages.length} messages found`);
    
    // POST new message
    const newMessage = {
      name: 'Test Customer',
      email: '<EMAIL>',
      subject: 'Test Message',
      message: 'This is a test message',
      category: 'general',
      priority: 'medium'
    };
    
    const postMessageResponse = await fetch(`${BASE_URL}/api/messages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newMessage)
    });
    
    if (postMessageResponse.ok) {
      console.log(`✅ POST Message: Created new message`);
    } else {
      console.log(`❌ POST Message: Failed to create message`);
    }
  } catch (error) {
    console.log(`❌ Message System Error: ${error.message}`);
  }

  // Test 5: Test dashboard access
  console.log('\n🔍 Testing Dashboard Access...');
  
  try {
    // Test admin dashboard (should redirect to login)
    const adminResponse = await fetch(`${BASE_URL}/admin`);
    if (adminResponse.url.includes('/login')) {
      console.log('✅ Admin Dashboard: Properly redirects to login when not authenticated');
    } else {
      console.log('❌ Admin Dashboard: Does not redirect to login');
    }
    
    // Test customer dashboard (should redirect to login)
    const customerResponse = await fetch(`${BASE_URL}/dashboard`);
    if (customerResponse.url.includes('/login')) {
      console.log('✅ Customer Dashboard: Properly redirects to login when not authenticated');
    } else {
      console.log('❌ Customer Dashboard: Does not redirect to login');
    }
  } catch (error) {
    console.log(`❌ Dashboard Access Error: ${error.message}`);
  }

  console.log('\n🏁 Tests completed!');
}

// Run the tests
testAPI().catch(console.error);
