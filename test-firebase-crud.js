// Test script to verify Firebase-based CRUD operations
// This script tests the new Firebase implementation

const BASE_URL = 'http://localhost:3000';

// Test credentials
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

const testProduct = {
  name: 'Test Product',
  description: 'This is a test product created via API',
  price: 99.99,
  category: 'Electronics',
  brand: 'TestBrand',
  stock: 10,
  featured: true,
  tags: ['test', 'api', 'firebase'],
  specifications: {
    'Color': 'Black',
    'Material': 'Plastic'
  },
  images: [{
    id: '1',
    url: 'https://via.placeholder.com/400x400',
    public_id: 'test-image',
    alt: 'Test Product',
    isPrimary: true
  }]
};

const testMessage = {
  name: 'Test User',
  email: '<EMAIL>',
  subject: 'Test Message',
  message: 'This is a test message to verify the contact system works.',
  priority: 'medium',
  category: 'general'
};

// Helper function to make HTTP requests
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
    
    const data = response.ok ? await response.json() : null;
    
    return {
      status: response.status,
      ok: response.ok,
      data
    };
  } catch (error) {
    console.error('Request failed:', error);
    return { status: 500, ok: false, data: null };
  }
}

// Test Firebase CRUD operations
async function testFirebaseCRUD() {
  console.log('🔥 Testing Firebase CRUD Operations...\n');
  
  // First, seed the database
  console.log('1. Seeding database with sample data...');
  const seedResponse = await makeRequest(`${BASE_URL}/api/products/seed`, {
    method: 'POST'
  });
  
  if (seedResponse.ok) {
    console.log('✅ Database seeded successfully');
    console.log(`   Data: ${JSON.stringify(seedResponse.data, null, 2)}`);
  } else {
    console.log('❌ Failed to seed database');
  }
  
  // Test Products CRUD
  console.log('\n2. Testing Products CRUD...');
  
  // Create a product
  console.log('\n2.1 Creating a new product...');
  const createResponse = await makeRequest(`${BASE_URL}/api/products`, {
    method: 'POST',
    body: JSON.stringify(testProduct)
  });
  
  let createdProductId;
  if (createResponse.ok) {
    createdProductId = createResponse.data.id;
    console.log(`✅ Product created successfully with ID: ${createdProductId}`);
  } else {
    console.log('❌ Failed to create product');
    console.log(`   Status: ${createResponse.status}`);
    console.log(`   Error: ${JSON.stringify(createResponse.data)}`);
  }
  
  // Get all products
  console.log('\n2.2 Fetching all products...');
  const getAllResponse = await makeRequest(`${BASE_URL}/api/products`);
  
  if (getAllResponse.ok) {
    console.log(`✅ Fetched ${getAllResponse.data.length} products`);
    console.log(`   Sample product: ${getAllResponse.data[0]?.name || 'None'}`);
  } else {
    console.log('❌ Failed to fetch products');
  }
  
  // Get products by category
  console.log('\n2.3 Fetching products by category...');
  const getCategoryResponse = await makeRequest(`${BASE_URL}/api/products?category=Electronics`);
  
  if (getCategoryResponse.ok) {
    console.log(`✅ Fetched ${getCategoryResponse.data.length} Electronics products`);
  } else {
    console.log('❌ Failed to fetch products by category');
  }
  
  // Get featured products
  console.log('\n2.4 Fetching featured products...');
  const getFeaturedResponse = await makeRequest(`${BASE_URL}/api/products?featured=true`);
  
  if (getFeaturedResponse.ok) {
    console.log(`✅ Fetched ${getFeaturedResponse.data.length} featured products`);
  } else {
    console.log('❌ Failed to fetch featured products');
  }
  
  // Search products
  console.log('\n2.5 Searching products...');
  const searchResponse = await makeRequest(`${BASE_URL}/api/products?search=headphones`);
  
  if (searchResponse.ok) {
    console.log(`✅ Found ${searchResponse.data.length} products matching 'headphones'`);
  } else {
    console.log('❌ Failed to search products');
  }
  
  // Update a product
  if (createdProductId) {
    console.log('\n2.6 Updating the created product...');
    const updateData = {
      name: 'Updated Test Product',
      price: 149.99
    };
    
    const updateResponse = await makeRequest(`${BASE_URL}/api/products?id=${createdProductId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
    
    if (updateResponse.ok) {
      console.log('✅ Product updated successfully');
      console.log(`   New name: ${updateResponse.data.name}`);
      console.log(`   New price: $${updateResponse.data.price}`);
    } else {
      console.log('❌ Failed to update product');
    }
  }
  
  // Test Messages CRUD
  console.log('\n3. Testing Messages CRUD...');
  
  // Create a message
  console.log('\n3.1 Creating a new message...');
  const createMessageResponse = await makeRequest(`${BASE_URL}/api/messages`, {
    method: 'POST',
    body: JSON.stringify(testMessage)
  });
  
  let createdMessageId;
  if (createMessageResponse.ok) {
    createdMessageId = createMessageResponse.data.id;
    console.log(`✅ Message created successfully with ID: ${createdMessageId}`);
  } else {
    console.log('❌ Failed to create message');
    console.log(`   Status: ${createMessageResponse.status}`);
    console.log(`   Error: ${JSON.stringify(createMessageResponse.data)}`);
  }
  
  // Get all messages
  console.log('\n3.2 Fetching all messages...');
  const getAllMessagesResponse = await makeRequest(`${BASE_URL}/api/messages`);
  
  if (getAllMessagesResponse.ok) {
    console.log(`✅ Fetched ${getAllMessagesResponse.data.length} messages`);
    console.log(`   Latest message: ${getAllMessagesResponse.data[0]?.subject || 'None'}`);
  } else {
    console.log('❌ Failed to fetch messages');
  }
  
  // Get messages by status
  console.log('\n3.3 Fetching unread messages...');
  const getUnreadResponse = await makeRequest(`${BASE_URL}/api/messages?status=unread`);
  
  if (getUnreadResponse.ok) {
    console.log(`✅ Fetched ${getUnreadResponse.data.length} unread messages`);
  } else {
    console.log('❌ Failed to fetch unread messages');
  }
  
  // Update message status
  if (createdMessageId) {
    console.log('\n3.4 Updating message status...');
    const updateMessageData = {
      status: 'read'
    };
    
    const updateMessageResponse = await makeRequest(`${BASE_URL}/api/messages?id=${createdMessageId}`, {
      method: 'PUT',
      body: JSON.stringify(updateMessageData)
    });
    
    if (updateMessageResponse.ok) {
      console.log('✅ Message status updated successfully');
      console.log(`   New status: ${updateMessageResponse.data.status}`);
    } else {
      console.log('❌ Failed to update message status');
    }
  }
  
  // Clean up - delete test items
  console.log('\n4. Cleaning up test data...');
  
  if (createdProductId) {
    console.log('\n4.1 Deleting test product...');
    const deleteProductResponse = await makeRequest(`${BASE_URL}/api/products?id=${createdProductId}`, {
      method: 'DELETE'
    });
    
    if (deleteProductResponse.ok) {
      console.log('✅ Test product deleted successfully');
    } else {
      console.log('❌ Failed to delete test product');
    }
  }
  
  if (createdMessageId) {
    console.log('\n4.2 Deleting test message...');
    const deleteMessageResponse = await makeRequest(`${BASE_URL}/api/messages?id=${createdMessageId}`, {
      method: 'DELETE'
    });
    
    if (deleteMessageResponse.ok) {
      console.log('✅ Test message deleted successfully');
    } else {
      console.log('❌ Failed to delete test message');
    }
  }
  
  console.log('\n🎉 Firebase CRUD testing completed!');
  console.log('\n📋 Summary:');
  console.log('- Database seeding tested');
  console.log('- Products CRUD operations tested');
  console.log('- Messages CRUD operations tested');
  console.log('- Search and filtering tested');
  console.log('- Data cleanup performed');
}

// Run the test
console.log('🚀 Starting Firebase CRUD Tests...');
console.log('Make sure your development server is running on http://localhost:3000\n');

testFirebaseCRUD().catch(console.error);
